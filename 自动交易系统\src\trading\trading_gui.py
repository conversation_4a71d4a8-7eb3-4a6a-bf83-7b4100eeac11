#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易系统GUI界面模块
提供用户界面和交互功能
"""

import sys
import os
import logging
import time
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QTextEdit, QPlainTextEdit, QGroupBox, QMessageBox, 
                            QStatusBar, QMenuBar, QAction, QSplitter, QTabWidget,
                            QProgressBar, QComboBox, QSpinBox, QCheckBox)
from PyQt5.QtCore import QTimer, Qt, QSettings
from PyQt5.QtGui import QFont, QIcon, QPixmap
import json

# 修复导入路径
try:
    from trading_engine import SmartTradingEngine
    from trading_thread import TradingThread, AdvancedTradingThread
except ImportError:
    from .trading_engine import SmartTradingEngine
    from .trading_thread import TradingThread, AdvancedTradingThread


class SmartTradingWindow(QMainWindow):
    """智能交易系统主窗口"""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)

        # 初始化组件
        self.trading_engine = None
        self.trading_thread = None
        self.settings = QSettings('SmartTrading', 'TradingSystem')

        # 初始化UI
        self.init_ui()

        # 延迟初始化其他组件，避免阻塞GUI
        QTimer.singleShot(100, self.delayed_init)

        # 状态定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新状态

    def delayed_init(self):
        """延迟初始化，避免GUI阻塞"""
        try:
            self.log_text.appendPlainText("🔧 正在加载配置...")
            QTimer.singleShot(50, self.load_settings)
            QTimer.singleShot(100, self.init_trading_engine)
        except Exception as e:
            self.log_text.appendPlainText(f"❌ 延迟初始化失败: {e}")
            self.logger.error(f"延迟初始化失败: {e}")
        
        self.logger.info("智能交易窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("景陶易购智能交易系统 v2.0")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置应用图标（如果有的话）
        try:
            self.setWindowIcon(QIcon('assets/icon.png'))
        except:
            pass
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        control_panel = self.create_control_panel()
        splitter.addWidget(control_panel)
        
        # 右侧信息面板
        info_panel = self.create_info_panel()
        splitter.addWidget(info_panel)
        
        # 设置分割比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 2)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 应用样式
        self.apply_styles()
    
    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 系统控制组
        control_group = QGroupBox("🚀 系统控制")
        control_layout = QVBoxLayout(control_group)
        
        # 主要控制按钮
        self.start_button = QPushButton("启动智能交易")
        self.start_button.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        self.start_button.setMinimumHeight(50)
        self.start_button.clicked.connect(self.start_trading)
        control_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止交易")
        self.stop_button.setFont(QFont("Microsoft YaHei", 12))
        self.stop_button.setMinimumHeight(40)
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_trading)
        control_layout.addWidget(self.stop_button)
        
        # 测试按钮
        button_layout = QHBoxLayout()
        
        self.test_coords_button = QPushButton("测试坐标")
        self.test_coords_button.clicked.connect(self.test_coordinates)
        button_layout.addWidget(self.test_coords_button)
        
        self.test_detection_button = QPushButton("测试检测")
        self.test_detection_button.clicked.connect(self.test_detection)
        button_layout.addWidget(self.test_detection_button)
        
        # 坐标校准按钮
        self.calibrate_button = QPushButton("坐标校准")
        self.calibrate_button.clicked.connect(self.open_coordinate_calibrator)
        self.calibrate_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        button_layout.addWidget(self.calibrate_button)
        
        control_layout.addLayout(button_layout)
        
        layout.addWidget(control_group)
        
        # 配置组
        config_group = QGroupBox("⚙️ 配置设置")
        config_layout = QVBoxLayout(config_group)
        
        # 监测间隔设置
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("监测间隔(秒):"))
        self.interval_spinbox = QSpinBox()
        self.interval_spinbox.setRange(1, 60)
        self.interval_spinbox.setValue(5)
        interval_layout.addWidget(self.interval_spinbox)
        config_layout.addLayout(interval_layout)
        
        # 调试模式
        self.debug_checkbox = QCheckBox("调试模式")
        config_layout.addWidget(self.debug_checkbox)
        
        # 自动截图
        self.screenshot_checkbox = QCheckBox("自动截图")
        self.screenshot_checkbox.setChecked(True)
        config_layout.addWidget(self.screenshot_checkbox)
        
        # 高级模式
        self.advanced_checkbox = QCheckBox("高级监测模式")
        config_layout.addWidget(self.advanced_checkbox)
        
        layout.addWidget(config_group)
        
        # 统计信息组
        stats_group = QGroupBox("📊 运行统计")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_labels = {
            'uptime': QLabel("运行时间: 00:00:00"),
            'trades': QLabel("交易次数: 0"),
            'signals': QLabel("信号数量: 0"),
            'success_rate': QLabel("成功率: 0%")
        }
        
        for label in self.stats_labels.values():
            label.setFont(QFont("Consolas", 9))
            stats_layout.addWidget(label)
        
        layout.addWidget(stats_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        return panel
    
    def create_info_panel(self) -> QWidget:
        """创建信息面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 日志标签页
        log_tab = QWidget()
        log_layout = QVBoxLayout(log_tab)
        
        # 日志文本区域
        self.log_text = QPlainTextEdit()
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setMaximumBlockCount(1000)  # 限制日志行数
        log_layout.addWidget(self.log_text)
        
        # 日志控制按钮
        log_button_layout = QHBoxLayout()
        
        clear_log_button = QPushButton("清空日志")
        clear_log_button.clicked.connect(self.clear_log)
        log_button_layout.addWidget(clear_log_button)
        
        save_log_button = QPushButton("保存日志")
        save_log_button.clicked.connect(self.save_log)
        log_button_layout.addWidget(save_log_button)
        
        log_button_layout.addStretch()
        log_layout.addLayout(log_button_layout)
        
        tab_widget.addTab(log_tab, "📋 运行日志")
        
        # 状态标签页
        status_tab = QWidget()
        status_layout = QVBoxLayout(status_tab)
        
        self.status_text = QTextEdit()
        self.status_text.setFont(QFont("Consolas", 9))
        self.status_text.setReadOnly(True)
        status_layout.addWidget(self.status_text)
        
        tab_widget.addTab(status_tab, "📊 系统状态")
        
        # 配置标签页
        config_tab = QWidget()
        config_layout = QVBoxLayout(config_tab)
        
        self.config_text = QTextEdit()
        self.config_text.setFont(QFont("Consolas", 9))
        config_layout.addWidget(self.config_text)
        
        load_config_button = QPushButton("重新加载配置")
        load_config_button.clicked.connect(self.reload_config)
        config_layout.addWidget(load_config_button)
        
        tab_widget.addTab(config_tab, "⚙️ 配置信息")
        
        layout.addWidget(tab_widget)
        
        return panel
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        # 导入配置
        import_action = QAction('导入配置...', self)
        import_action.triggered.connect(self.import_config)
        file_menu.addAction(import_action)
        
        # 导出配置
        export_action = QAction('导出配置...', self)
        export_action.triggered.connect(self.export_config)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具(&T)')
        
        # 坐标校准
        calibrate_action = QAction('坐标校准...', self)
        calibrate_action.triggered.connect(self.open_calibration)
        tools_menu.addAction(calibrate_action)
        
        # 检测测试
        detection_action = QAction('检测测试...', self)
        detection_action.triggered.connect(self.open_detection_test)
        tools_menu.addAction(detection_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        # 关于
        about_action = QAction('关于...', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 客户端状态
        self.client_status_label = QLabel("客户端: 未连接")
        self.status_bar.addPermanentWidget(self.client_status_label)
        
        # 时间标签
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
    
    def apply_styles(self):
        """应用界面样式"""
        style = """
        QMainWindow {
            background-color: #f0f0f0;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 8px 16px;
            text-align: center;
            text-decoration: none;
            font-size: 12px;
            margin: 2px;
            border-radius: 4px;
        }
        
        QPushButton:hover {
            background-color: #45a049;
        }
        
        QPushButton:pressed {
            background-color: #3d8b40;
        }
        
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
        
        #start_button {
            background-color: #2196F3;
            font-size: 14px;
        }
        
        #start_button:hover {
            background-color: #1976D2;
        }
        
        #stop_button {
            background-color: #f44336;
        }
        
        #stop_button:hover {
            background-color: #d32f2f;
        }
        
        QTextEdit {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            background-color: white;
        }
        
        QTabWidget::pane {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        QTabBar::tab {
            background-color: #e0e0e0;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        
        QTabBar::tab:selected {
            background-color: white;
            border-bottom: 2px solid #2196F3;
        }
        """
        
        self.setStyleSheet(style)
        
        # 为特定按钮设置ID
        self.start_button.setObjectName("start_button")
        self.stop_button.setObjectName("stop_button")
    
    def init_trading_engine(self):
        """初始化交易引擎"""
        try:
            self.log_text.appendPlainText("🔧 正在初始化交易引擎...")

            # 延迟初始化，避免GUI卡死
            QTimer.singleShot(100, self._delayed_init_trading_engine)

        except Exception as e:
            self.log_text.appendPlainText(f"❌ 交易引擎初始化失败: {e}")
            self.logger.error(f"交易引擎初始化失败: {e}")

    def _delayed_init_trading_engine(self):
        """延迟初始化交易引擎"""
        try:
            # 创建简单配置对象，避免复杂的配置管理器初始化
            class SimpleConfig:
                def get(self, key, default=None):
                    # 返回一些基本的默认配置
                    config_map = {
                        'price_detection.use_manual_price_selection': False,
                        'trading.angle_threshold': 20,
                        'trading.default_quantity': 1,
                        'trading.check_interval': 5
                    }
                    return config_map.get(key, default)

            config = SimpleConfig()
            self.trading_engine = SmartTradingEngine(config=config)
            self.log_text.appendPlainText("✅ 交易引擎基础初始化成功")

            # 再次延迟初始化其他组件
            QTimer.singleShot(500, self._delayed_init_components)

        except Exception as e:
            self.log_text.appendPlainText(f"❌ 交易引擎延迟初始化失败: {e}")
            self.logger.error(f"交易引擎延迟初始化失败: {e}")

    def _delayed_init_components(self):
        """延迟初始化其他组件"""
        try:
            if self.trading_engine:
                self.trading_engine.delayed_init_components()
                self.log_text.appendPlainText("✅ 交易引擎完全初始化成功")

        except Exception as e:
            self.log_text.appendPlainText(f"⚠️ 组件延迟初始化失败: {e}")
            self.logger.warning(f"组件延迟初始化失败: {e}")
    
    def start_trading(self):
        """启动交易"""
        try:
            if not self.trading_engine:
                QMessageBox.warning(self, "错误", "交易引擎未初始化")
                return
            
            self.log_text.appendPlainText("🚀 正在启动智能交易...")
            
            # 获取配置
            config = {
                'monitor_interval': self.interval_spinbox.value(),
                'debug_mode': self.debug_checkbox.isChecked(),
                'auto_screenshot': self.screenshot_checkbox.isChecked()
            }
            
            # 创建交易线程
            if self.advanced_checkbox.isChecked():
                self.trading_thread = AdvancedTradingThread(self.trading_engine, config)
                self.log_text.appendPlainText("🔧 使用高级监测模式")
            else:
                self.trading_thread = TradingThread(self.trading_engine)
                self.log_text.appendPlainText("🔧 使用标准监测模式")
            
            # 连接信号
            self.trading_thread.log_signal.connect(self.log_text.appendPlainText)
            self.trading_thread.status_signal.connect(self.update_trading_status)
            
            # 启动线程
            self.trading_thread.start()
            
            # 更新UI状态
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("运行中...")
            
            self.log_text.appendPlainText("✅ 智能交易已启动")
            
        except Exception as e:
            error_msg = f"启动交易失败: {e}"
            self.log_text.appendPlainText(f"❌ {error_msg}")
            QMessageBox.critical(self, "错误", error_msg)
    
    def stop_trading(self):
        """停止交易"""
        try:
            if self.trading_thread and self.trading_thread.isRunning():
                self.log_text.appendPlainText("⏹️ 正在停止交易...")
                self.trading_thread.stop()
                
                # 等待线程结束
                if self.trading_thread.wait(5000):  # 等待5秒
                    self.log_text.appendPlainText("✅ 交易已停止")
                else:
                    self.log_text.appendPlainText("⚠️ 强制终止交易线程")
                    self.trading_thread.terminate()
            
            # 更新UI状态
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText("已停止")
            
        except Exception as e:
            error_msg = f"停止交易失败: {e}"
            self.log_text.appendPlainText(f"❌ {error_msg}")
            QMessageBox.critical(self, "错误", error_msg)
    
    def test_coordinates(self):
        """测试坐标"""
        try:
            if not self.trading_engine:
                QMessageBox.warning(self, "错误", "交易引擎未初始化")
                return
            
            # 询问用户选择测试方式
            reply = QMessageBox.question(self, "坐标测试", 
                                       "选择测试方式:\n\n"
                                       "✅ 是 - 鼠标移动测试 (可视化，鼠标会依次移动到各按钮)\n"
                                       "❌ 否 - 快速验证测试 (仅检查坐标计算)",
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.Yes)
            
            if reply == QMessageBox.Yes:
                # 鼠标移动测试
                self.log_text.appendPlainText("🎯 开始鼠标移动测试...")
                self.log_text.appendPlainText("⚠️ 请确保景陶易购客户端窗口可见，鼠标将依次移动到各按钮位置")
                
                # 给用户3秒准备时间
                for i in range(3, 0, -1):
                    self.log_text.appendPlainText(f"⏰ {i}秒后开始测试...")
                    QApplication.processEvents()
                    time.sleep(1)
                
                test_result = self.trading_engine.test_coordinates_with_mouse()
                
                if test_result:
                    self.log_text.appendPlainText("✅ 鼠标移动测试完成")
                    QMessageBox.information(self, "测试结果", "✅ 鼠标移动测试完成！\n\n请检查鼠标移动的位置是否准确对应各按钮。")
                else:
                    self.log_text.appendPlainText("❌ 鼠标移动测试失败")
                    QMessageBox.warning(self, "测试结果", "❌ 鼠标移动测试失败，请查看日志")
            else:
                # 快速验证测试
                self.log_text.appendPlainText("🧪 开始快速坐标验证...")
                
                all_buttons = [
                    'buy_mode_button', 'sell_mode_button',
                    'buy_order_button', 'sell_order_button', 'price_input', 'quantity_input',
                    'confirm_button', 'transfer_out_button', 'order_mode_button'
                ]
                
                test_result = self.trading_engine.test_coordinates(all_buttons)
                
                if test_result:
                    self.log_text.appendPlainText("✅ 坐标验证通过")
                    QMessageBox.information(self, "测试结果", "✅ 坐标验证通过，所有按钮位置计算正确")
                else:
                    self.log_text.appendPlainText("⚠️ 坐标验证发现问题，请查看详细日志")
                    QMessageBox.warning(self, "测试结果", "⚠️ 坐标验证发现问题，部分按钮位置可能不准确\n\n请查看详细日志或考虑重新校准")
            
        except Exception as e:
            error_msg = f"坐标测试失败: {e}"
            self.log_text.appendPlainText(f"❌ {error_msg}")
            QMessageBox.critical(self, "错误", error_msg)
    
    def test_detection(self):
        """测试检测功能"""
        try:
            self.log_text.appendPlainText("🔍 开始测试检测功能...")
            
            if not self.trading_engine:
                QMessageBox.warning(self, "错误", "交易引擎未初始化")
                return
            
            # 简单的检测测试
            import pyautogui
            import numpy as np
            
            # 捕获屏幕
            screen = pyautogui.screenshot()
            screen_array = np.array(screen)
            
            # 测试黄线检测
            yellow_result = self.trading_engine.detect_yellow_line_change(screen_array)
            self.log_text.appendPlainText(f"🟡 黄线检测结果: {yellow_result}")
            
            # 测试景陶易购检测
            if hasattr(self.trading_engine, 'detect_jingtao_signals'):
                jingtao_result = self.trading_engine.detect_jingtao_signals(screen_array)
                self.log_text.appendPlainText(f"🎯 景陶易购检测结果: {jingtao_result}")
            
            self.log_text.appendPlainText("✅ 检测功能测试完成")
            
        except Exception as e:
            error_msg = f"检测测试失败: {e}"
            self.log_text.appendPlainText(f"❌ {error_msg}")
            QMessageBox.critical(self, "错误", error_msg)
    
    def update_status(self):
        """更新状态信息"""
        try:
            # 更新时间
            from datetime import datetime
            current_time = datetime.now().strftime("%H:%M:%S")
            self.time_label.setText(current_time)
            
            # 更新客户端状态
            if self.trading_engine and self.trading_engine.client_window:
                self.client_status_label.setText("客户端: 已连接")
            else:
                self.client_status_label.setText("客户端: 未连接")
            
            # 更新运行统计
            self.update_statistics()
            
            # 更新系统状态
            self.update_system_status()
            
        except Exception as e:
            self.logger.error(f"状态更新失败: {e}")
    
    def update_statistics(self):
        """更新运行统计"""
        try:
            # 这里可以添加真实的统计逻辑
            if hasattr(self, 'start_time'):
                from datetime import datetime
                uptime = datetime.now() - self.start_time
                self.stats_labels['uptime'].setText(f"运行时间: {str(uptime).split('.')[0]}")
            
        except Exception as e:
            self.logger.error(f"统计更新失败: {e}")
    
    def update_system_status(self):
        """更新系统状态"""
        try:
            status_info = []
            
            # 系统基本信息
            status_info.append("=== 系统状态 ===")
            status_info.append(f"交易引擎: {'正常' if self.trading_engine else '未初始化'}")
            status_info.append(f"监测线程: {'运行中' if self.trading_thread and self.trading_thread.isRunning() else '已停止'}")
            
            # 配置信息
            status_info.append("\n=== 当前配置 ===")
            status_info.append(f"监测间隔: {self.interval_spinbox.value()}秒")
            status_info.append(f"调试模式: {'开启' if self.debug_checkbox.isChecked() else '关闭'}")
            status_info.append(f"自动截图: {'开启' if self.screenshot_checkbox.isChecked() else '关闭'}")
            status_info.append(f"高级模式: {'开启' if self.advanced_checkbox.isChecked() else '关闭'}")
            
            # 线程状态
            if self.trading_thread:
                thread_status = self.trading_thread.get_thread_status()
                status_info.append("\n=== 线程状态 ===")
                for key, value in thread_status.items():
                    status_info.append(f"{key}: {value}")
            
            self.status_text.setPlainText('\n'.join(status_info))
            
        except Exception as e:
            self.logger.error(f"系统状态更新失败: {e}")
    
    def update_trading_status(self, status: str):
        """更新交易状态"""
        self.status_label.setText(status)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.log_text.appendPlainText("📋 日志已清空")
    
    def save_log(self):
        """保存日志"""
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"logs/trading_log_{timestamp}.txt"
            
            # 确保logs目录存在
            os.makedirs("logs", exist_ok=True)
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.toPlainText())
            
            self.log_text.appendPlainText(f"💾 日志已保存到: {filename}")
            QMessageBox.information(self, "保存成功", f"日志已保存到: {filename}")
            
        except Exception as e:
            error_msg = f"保存日志失败: {e}"
            self.log_text.appendPlainText(f"❌ {error_msg}")
            QMessageBox.critical(self, "错误", error_msg)
    
    def reload_config(self):
        """重新加载配置"""
        try:
            if self.trading_engine:
                # 重新加载坐标配置
                self.trading_engine.relative_positions = self.trading_engine.load_coordinate_config()
                self.trading_engine.validate_coordinates()
                
                # 显示配置信息
                config_info = json.dumps(self.trading_engine.relative_positions, indent=2, ensure_ascii=False)
                self.config_text.setPlainText(config_info)
                
                self.log_text.appendPlainText("✅ 配置重新加载完成")
            
        except Exception as e:
            error_msg = f"重新加载配置失败: {e}"
            self.log_text.appendPlainText(f"❌ {error_msg}")
            QMessageBox.critical(self, "错误", error_msg)
    
    def load_settings(self):
        """加载设置"""
        try:
            # 恢复窗口状态
            geometry = self.settings.value('geometry')
            if geometry:
                self.restoreGeometry(geometry)
            
            # 恢复配置选项
            self.interval_spinbox.setValue(self.settings.value('monitor_interval', 5, type=int))
            self.debug_checkbox.setChecked(self.settings.value('debug_mode', False, type=bool))
            self.screenshot_checkbox.setChecked(self.settings.value('auto_screenshot', True, type=bool))
            self.advanced_checkbox.setChecked(self.settings.value('advanced_mode', False, type=bool))
            
        except Exception as e:
            self.logger.error(f"加载设置失败: {e}")
    
    def save_settings(self):
        """保存设置"""
        try:
            # 保存窗口状态
            self.settings.setValue('geometry', self.saveGeometry())
            
            # 保存配置选项
            self.settings.setValue('monitor_interval', self.interval_spinbox.value())
            self.settings.setValue('debug_mode', self.debug_checkbox.isChecked())
            self.settings.setValue('auto_screenshot', self.screenshot_checkbox.isChecked())
            self.settings.setValue('advanced_mode', self.advanced_checkbox.isChecked())
            
        except Exception as e:
            self.logger.error(f"保存设置失败: {e}")
    
    def import_config(self):
        """导入配置"""
        # TODO: 实现配置导入功能
        QMessageBox.information(self, "功能开发中", "配置导入功能正在开发中...")
    
    def export_config(self):
        """导出配置"""
        # TODO: 实现配置导出功能
        QMessageBox.information(self, "功能开发中", "配置导出功能正在开发中...")
    
    def open_calibration(self):
        """打开坐标校准"""
        # TODO: 实现坐标校准功能
        QMessageBox.information(self, "功能开发中", "坐标校准功能正在开发中...")
    
    def open_detection_test(self):
        """打开检测测试"""
        # TODO: 实现检测测试功能
        QMessageBox.information(self, "功能开发中", "检测测试功能正在开发中...")
    
    def show_about(self):
        """显示关于对话框"""
        about_text = """
        <h3>景陶易购智能交易系统 v2.0</h3>
        <p>一个智能化的自动交易系统，支持多种交易策略和风险管理。</p>
        
        <p><b>主要功能：</b></p>
        <ul>
        <li>智能信号检测</li>
        <li>自动交易执行</li>
        <li>风险管理控制</li>
        <li>实时监控报警</li>
        </ul>
        
        <p><b>技术栈：</b></p>
        <ul>
        <li>Python 3.9+</li>
        <li>PyQt5</li>
        <li>OpenCV</li>
        <li>NumPy</li>
        </ul>
        
        <p><small>版权所有 © 2025</small></p>
        """
        
        QMessageBox.about(self, "关于", about_text)
    
    def open_coordinate_calibrator(self):
        """打开坐标校准工具"""
        try:
            # 询问用户选择恢复还是重新校准
            reply = QMessageBox.question(self, "坐标校准", 
                                       "选择操作:\n\n"
                                       "✅ 是 - 恢复已验证的坐标配置 (推荐)\n"
                                       "❌ 否 - 启动坐标校准工具 (重新校准)",
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.Yes)
            
            if reply == QMessageBox.Yes:
                # 恢复已验证的坐标
                self.restore_coordinates()
            else:
                # 启动校准工具
                self.start_calibration_tool()
            
        except Exception as e:
            error_msg = f"坐标校准失败: {e}"
            self.logger.error(error_msg)
            self.log_text.appendPlainText(f"❌ {error_msg}")
            QMessageBox.critical(self, "坐标校准错误", error_msg)
    
    def restore_coordinates(self):
        """恢复已验证的坐标配置"""
        try:
            self.log_text.appendPlainText("🔄 正在恢复已验证的坐标配置...")
            
            if not self.trading_engine:
                QMessageBox.warning(self, "错误", "交易引擎未初始化")
                return
            
            # 先备份当前坐标
            self.trading_engine.save_current_coordinates_as_backup()
            
            # 恢复验证过的坐标
            if self.trading_engine.restore_verified_coordinates():
                self.log_text.appendPlainText("✅ 坐标恢复成功！")
                
                # 测试恢复的坐标
                if self.trading_engine.test_coordinates():
                    self.log_text.appendPlainText("✅ 恢复的坐标验证通过")
                    QMessageBox.information(self, "恢复成功", 
                                          "✅ 坐标恢复成功！\n\n"
                                          "已恢复到验证通过的坐标配置。\n"
                                          "当前坐标已自动备份到 current_coordinates_backup.json")
                else:
                    self.log_text.appendPlainText("⚠️ 恢复的坐标需要进一步验证")
                    QMessageBox.warning(self, "提醒", 
                                      "坐标已恢复但验证时出现警告。\n\n"
                                      "建议使用'测试坐标'功能进行验证。")
            else:
                self.log_text.appendPlainText("❌ 坐标恢复失败")
                QMessageBox.critical(self, "恢复失败", 
                                   "❌ 坐标恢复失败！\n\n"
                                   "请检查 backup_coordinates.json 文件是否存在。")
                
        except Exception as e:
            error_msg = f"恢复坐标失败: {e}"
            self.logger.error(error_msg)
            self.log_text.appendPlainText(f"❌ {error_msg}")
            QMessageBox.critical(self, "恢复失败", error_msg)
    
    def start_calibration_tool(self):
        """启动坐标校准工具"""
        try:
            self.log_text.appendPlainText("🎯 启动坐标校准工具...")
            
            # 导入校准工具模块
            import sys
            from pathlib import Path
            
            # 添加utils路径
            utils_path = Path(__file__).parent.parent.parent / "utils"
            if str(utils_path) not in sys.path:
                sys.path.insert(0, str(utils_path))
            
            from coordinate_calibrator import CoordinateCalibrator
            
            # 创建校准器实例
            self.calibrator = CoordinateCalibrator()
            
            # 运行校准工具
            self.calibrator.run()
            
            # 校准完成后重新加载配置
            self.reload_config()
            self.log_text.appendPlainText("✅ 坐标校准工具已关闭，配置已重新加载")
            
        except Exception as e:
            error_msg = f"启动坐标校准工具失败: {e}"
            self.logger.error(error_msg)
            self.log_text.appendPlainText(f"❌ {error_msg}")
            QMessageBox.critical(self, "校准工具错误", 
                               f"无法启动坐标校准工具:\n{e}\n\n"
                               f"请检查 utils/coordinate_calibrator.py 文件是否存在")
    
    def closeEvent(self, event):
        """关闭事件"""
        try:
            # 停止交易
            if self.trading_thread and self.trading_thread.isRunning():
                reply = QMessageBox.question(self, '确认退出', 
                                           '交易正在运行中，确定要退出吗？',
                                           QMessageBox.Yes | QMessageBox.No)
                if reply == QMessageBox.No:
                    event.ignore()
                    return
                
                self.stop_trading()
            
            # 保存设置
            self.save_settings()
            
            # 接受关闭事件
            event.accept()
            
        except Exception as e:
            self.logger.error(f"关闭事件处理失败: {e}")
            event.accept()


if __name__ == "__main__":
    # 测试代码
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("智能交易系统")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("SmartTrading")
    
    # 创建主窗口
    window = SmartTradingWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())