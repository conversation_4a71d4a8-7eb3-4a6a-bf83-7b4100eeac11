<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="43d6d208-7d8d-44c5-b1a1-4681b2e5e349" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="30ZvqRBsoV8tN0dvsIM9Wk8hybI" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.calibration_tool.executor": "Run",
    "Python.client_detection_test.executor": "Run",
    "Python.confirm_button_calibrator.executor": "Run",
    "Python.coordinate_calibrator.executor": "Run",
    "Python.debug_color_detection.executor": "Run",
    "Python.debug_import.executor": "Run",
    "Python.demo_manual_price.executor": "Run",
    "Python.direct_client_controller.executor": "Run",
    "Python.enhanced_red_green_macd.executor": "Run",
    "Python.get_coordinates.executor": "Run",
    "Python.main.executor": "Run",
    "Python.main_simple.executor": "Run",
    "Python.main_stable (1).executor": "Run",
    "Python.main_stable.executor": "Run",
    "Python.main_stable_manual.executor": "Run",
    "Python.main_window.executor": "Run",
    "Python.minimal_engine.executor": "Run",
    "Python.price_detection_test.executor": "Run",
    "Python.price_region_selector.executor": "Run",
    "Python.project_cleanup.executor": "Run",
    "Python.quick_fix_guide.executor": "Run",
    "Python.quick_test.executor": "Run",
    "Python.region_selector.executor": "Run",
    "Python.reorganize_project.executor": "Run",
    "Python.simple_calibration.executor": "Run",
    "Python.smart_trading (1).executor": "Run",
    "Python.smart_trading.executor": "Run",
    "Python.smart_trading_complete (1).executor": "Run",
    "Python.smart_trading_complete.executor": "Run",
    "Python.smart_trading_engine.executor": "Run",
    "Python.start.executor": "Run",
    "Python.start_smart_trading.executor": "Run",
    "Python.strategy_implementations.executor": "Run",
    "Python.test_auto_confirm.executor": "Run",
    "Python.test_calibration.executor": "Run",
    "Python.test_clean.executor": "Run",
    "Python.test_delayed_order_confirm.executor": "Run",
    "Python.test_detectors.executor": "Run",
    "Python.test_enhanced_detection.executor": "Run",
    "Python.test_gui_import.executor": "Run",
    "Python.test_gui_startup.executor": "Run",
    "Python.test_multi_strategy.executor": "Run",
    "Python.test_simple.executor": "Run",
    "Python.test_trade_execution.executor": "Run",
    "Python.test_updated_config.executor": "Run",
    "Python.trading_engine.executor": "Run",
    "Python.verify_coordinates.executor": "Run",
    "Python.window_position_fix.executor": "Run",
    "Python.启动系统.executor": "Run",
    "Python.性能测试.executor": "Run",
    "Python.测试多线程优化.executor": "Run",
    "Python.语法检查.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/软件开发",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "run.code.analysis.last.selected.profile": "pProject Default",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\软件开发\自动交易系统" />
    </key>
  </component>
  <component name="RunManager" selected="Python.main_stable">
    <configuration name="demo_manual_price" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="软件开发" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/自动交易系统" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/自动交易系统/demo_manual_price.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="main_stable" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="软件开发" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/自动交易系统" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/自动交易系统/main_stable.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="main_stable_manual" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="软件开发" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/自动交易系统" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/自动交易系统/main_stable_manual.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="price_region_selector" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="软件开发" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/自动交易系统/tools" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/自动交易系统/tools/price_region_selector.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_gui_startup" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="软件开发" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/自动交易系统" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/自动交易系统/test_gui_startup.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.main_stable" />
        <item itemvalue="Python.test_gui_startup" />
        <item itemvalue="Python.main_stable_manual" />
        <item itemvalue="Python.price_region_selector" />
        <item itemvalue="Python.demo_manual_price" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.18034.82" />
        <option value="bundled-python-sdk-975db3bf15a3-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.18034.82" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="43d6d208-7d8d-44c5-b1a1-4681b2e5e349" name="更改" comment="" />
      <created>1753848099596</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753848099596</updated>
      <workItem from="1753848100670" duration="23000" />
      <workItem from="1753848139549" duration="28386000" />
      <workItem from="1754019371834" duration="5323000" />
      <workItem from="1754035247348" duration="2481000" />
      <workItem from="1754273385813" duration="13508000" />
      <workItem from="1754297477226" duration="5614000" />
      <workItem from="1754361478529" duration="10374000" />
      <workItem from="1754376535983" duration="4375000" />
      <workItem from="1754383666667" duration="3251000" />
      <workItem from="1754391982121" duration="1262000" />
      <workItem from="1754396253789" duration="1806000" />
      <workItem from="1754398400998" duration="936000" />
      <workItem from="1754400320348" duration="1069000" />
      <workItem from="1754443152507" duration="2932000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/$trading_engine.coverage" NAME="trading_engine 覆盖结果" MODIFIED="1754379866713" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统/src/trading" />
    <SUITE FILE_PATH="coverage/$test_clean.coverage" NAME="test_clean 覆盖结果" MODIFIED="1754375665397" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$smart_trading.coverage" NAME="smart_trading 覆盖结果" MODIFIED="1754392148986" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$demo_manual_price.coverage" NAME="demo_manual_price 覆盖结果" MODIFIED="1754444133916" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$start.coverage" NAME="start 覆盖结果" MODIFIED="1753858908476" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$debug_import.coverage" NAME="debug_import 覆盖结果" MODIFIED="1754375508695" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$smart_trading_engine.coverage" NAME="smart_trading_engine 覆盖结果" MODIFIED="1754275489393" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统/core" />
    <SUITE FILE_PATH="coverage/$coordinate_calibrator.coverage" NAME="coordinate_calibrator 覆盖结果" MODIFIED="1754362685758" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统/tools" />
    <SUITE FILE_PATH="coverage/$test_enhanced_detection.coverage" NAME="test_enhanced_detection 覆盖结果" MODIFIED="1754033833467" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$simple_calibration.coverage" NAME="simple_calibration 覆盖结果" MODIFIED="1753853371280" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统/utils" />
    <SUITE FILE_PATH="coverage/$confirm_button_calibrator.coverage" NAME="confirm_button_calibrator 覆盖结果" MODIFIED="1754290375187" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$client_detection_test.coverage" NAME="client_detection_test 覆盖结果" MODIFIED="1754362932103" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统/tools" />
    <SUITE FILE_PATH="coverage/$test_delayed_order_confirm.coverage" NAME="test_delayed_order_confirm 覆盖结果" MODIFIED="1754276635811" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$debug_color_detection.coverage" NAME="debug_color_detection 覆盖结果" MODIFIED="1754370542133" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统/tools" />
    <SUITE FILE_PATH="coverage/$test_detectors.coverage" NAME="test_detectors 覆盖结果" MODIFIED="1754396928439" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$enhanced_red_green_macd.coverage" NAME="enhanced_red_green_macd 覆盖结果" MODIFIED="1754277796146" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$project_cleanup.coverage" NAME="project_cleanup 覆盖结果" MODIFIED="1754336945219" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$test_auto_confirm.coverage" NAME="test_auto_confirm 覆盖结果" MODIFIED="1754275242358" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$minimal_engine.coverage" NAME="minimal_engine 覆盖结果" MODIFIED="1754375424550" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$main_stable.coverage" NAME="main_stable 覆盖结果" MODIFIED="1754446336282" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$test_gui_import.coverage" NAME="test_gui_import 覆盖结果" MODIFIED="1754376801432" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$verify_coordinates.coverage" NAME="verify_coordinates 覆盖结果" MODIFIED="1754292168327" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$calibration_tool.coverage" NAME="calibration_tool 覆盖结果" MODIFIED="1753852971359" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统/utils" />
    <SUITE FILE_PATH="coverage/$direct_client_controller.coverage" NAME="direct_client_controller 覆盖结果" MODIFIED="1753862400010" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统/core" />
    <SUITE FILE_PATH="coverage/$start_smart_trading.coverage" NAME="start_smart_trading 覆盖结果" MODIFIED="1753889190446" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$test_updated_config.coverage" NAME="test_updated_config 覆盖结果" MODIFIED="1754362979626" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统/tools" />
    <SUITE FILE_PATH="coverage/$smart_trading__1_.coverage" NAME="smart_trading (1) 覆盖结果" MODIFIED="1754374431913" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$test_gui_startup.coverage" NAME="test_gui_startup 覆盖结果" MODIFIED="1754446313112" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$smart_trading_complete__1_.coverage" NAME="smart_trading_complete (1) 覆盖结果" MODIFIED="1754292246933" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$main.coverage" NAME="main 覆盖结果" MODIFIED="1754330049711" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$test_simple.coverage" NAME="test_simple 覆盖结果" MODIFIED="1754375176373" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$strategy_implementations.coverage" NAME="strategy_implementations 覆盖结果" MODIFIED="1754030669389" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$test_multi_strategy.coverage" NAME="test_multi_strategy 覆盖结果" MODIFIED="1754030633835" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$smart_trading_complete.coverage" NAME="smart_trading_complete 覆盖结果" MODIFIED="1754289124525" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$main_window.coverage" NAME="main_window 覆盖结果" MODIFIED="1753858901563" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统/gui" />
    <SUITE FILE_PATH="coverage/$get_coordinates.coverage" NAME="get_coordinates 覆盖结果" MODIFIED="1753854013126" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统/utils" />
    <SUITE FILE_PATH="coverage/$quick_test.coverage" NAME="quick_test 覆盖结果" MODIFIED="1754397150136" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$price_region_selector.coverage" NAME="price_region_selector 覆盖结果" MODIFIED="1754444303599" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统/tools" />
    <SUITE FILE_PATH="coverage/$window_position_fix.coverage" NAME="window_position_fix 覆盖结果" MODIFIED="1754363599059" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统/tools" />
    <SUITE FILE_PATH="coverage/$test_trade_execution.coverage" NAME="test_trade_execution 覆盖结果" MODIFIED="1753884590824" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$reorganize_project.coverage" NAME="reorganize_project 覆盖结果" MODIFIED="1754328638620" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$main_stable__1_.coverage" NAME="main_stable (1) 覆盖结果" MODIFIED="1754399096973" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统 (8)/自动交易系统" />
    <SUITE FILE_PATH="coverage/$test_calibration.coverage" NAME="test_calibration 覆盖结果" MODIFIED="1754291335400" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$main_simple.coverage" NAME="main_simple 覆盖结果" MODIFIED="1754329959185" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$region_selector.coverage" NAME="region_selector 覆盖结果" MODIFIED="1753976914881" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$.coverage" NAME="测试多线程优化 覆盖结果" MODIFIED="1754297217662" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$quick_fix_guide.coverage" NAME="quick_fix_guide 覆盖结果" MODIFIED="1754290272630" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
    <SUITE FILE_PATH="coverage/$price_detection_test.coverage" NAME="price_detection_test 覆盖结果" MODIFIED="1754379533486" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统/tools" />
    <SUITE FILE_PATH="coverage/$main_stable_manual.coverage" NAME="main_stable_manual 覆盖结果" MODIFIED="1754445086473" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/自动交易系统" />
  </component>
</project>