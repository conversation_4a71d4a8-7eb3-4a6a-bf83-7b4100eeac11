#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动确认处理器
智能检测并处理确认对话框，自动点击确定按钮
"""

import cv2
import numpy as np
import pyautogui
import time
import logging
import json
import os
from typing import Dict, Optional, Tuple
import win32gui
import win32con

class AutoConfirmHandler:
    """自动确认处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 确认对话框的特征文字
        self.confirm_keywords = [
            "确认下单",
            "是否继续", 
            "确定",
            "OK",
            "确认",
            "继续"
        ]
        
        # 确定按钮的特征文字
        self.ok_button_keywords = [
            "确定",
            "确认", 
            "是",
            "OK",
            "继续"
        ]
        
        # 取消按钮的特征文字  
        self.cancel_button_keywords = [
            "取消",
            "否", 
            "Cancel",
            "关闭"
        ]
        
        # 加载校准的坐标配置
        self.calibrated_positions = self._load_calibrated_positions()
        
        self.logger.info("自动确认处理器初始化完成")
    
    def detect_confirm_dialog(self, screenshot: np.ndarray = None) -> Dict:
        """检测确认对话框"""
        try:
            if screenshot is None:
                # 获取屏幕截图
                screenshot = pyautogui.screenshot()
                screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # 方法1: 检测对话框窗口
            dialog_info = self._detect_dialog_window()
            if dialog_info:
                self.logger.info(f"检测到对话框窗口: {dialog_info}")
                return dialog_info
            
            # 方法2: 颜色和结构检测
            dialog_info = self._detect_dialog_by_color(screenshot)
            if dialog_info:
                self.logger.info(f"通过颜色检测到对话框: {dialog_info}")
                return dialog_info
            
            # 方法3: 模板匹配
            dialog_info = self._detect_dialog_by_template(screenshot)
            if dialog_info:
                self.logger.info(f"通过模板匹配检测到对话框: {dialog_info}")
                return dialog_info
            
            return {'detected': False}
            
        except Exception as e:
            self.logger.error(f"检测确认对话框失败: {e}")
            return {'detected': False, 'error': str(e)}
    
    def _detect_dialog_window(self) -> Optional[Dict]:
        """通过窗口API检测对话框"""
        try:
            dialog_windows = []
            
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    
                    # 检查是否是对话框类型
                    if any(keyword in window_text for keyword in self.confirm_keywords):
                        rect = win32gui.GetWindowRect(hwnd)
                        windows.append({
                            'hwnd': hwnd,
                            'title': window_text,
                            'class': class_name,
                            'rect': rect
                        })
                return True
            
            win32gui.EnumWindows(enum_windows_callback, dialog_windows)
            
            if dialog_windows:
                # 返回第一个找到的对话框
                dialog = dialog_windows[0]
                return {
                    'detected': True,
                    'method': 'window_api',
                    'window_info': dialog,
                    'center_x': (dialog['rect'][0] + dialog['rect'][2]) // 2,
                    'center_y': (dialog['rect'][1] + dialog['rect'][3]) // 2
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"窗口API检测失败: {e}")
            return None
    
    def _detect_dialog_by_color(self, screenshot: np.ndarray) -> Optional[Dict]:
        """通过颜色特征检测对话框"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            
            # 检测矩形区域（对话框通常是矩形）
            _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
            
            # 查找轮廓
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                
                # 对话框大小过滤（不会太大也不会太小）
                if 10000 < area < 200000:
                    # 检查是否接近矩形
                    epsilon = 0.02 * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)
                    
                    if len(approx) >= 4:  # 至少4个角点，接近矩形
                        x, y, w, h = cv2.boundingRect(contour)
                        
                        # 宽高比检查（对话框通常宽度大于高度）
                        aspect_ratio = w / h
                        if 1.5 < aspect_ratio < 4.0:
                            
                            # 检查中心区域是否有对话框特征
                            center_region = screenshot[y:y+h, x:x+w]
                            if self._has_dialog_features(center_region):
                                return {
                                    'detected': True,
                                    'method': 'color_detection',
                                    'region': (x, y, w, h),
                                    'center_x': x + w // 2,
                                    'center_y': y + h // 2
                                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"颜色检测失败: {e}")
            return None
    
    def _detect_dialog_by_template(self, screenshot: np.ndarray) -> Optional[Dict]:
        """通过模板匹配检测对话框"""
        try:
            # 基于您的截图，对话框通常在屏幕中央
            height, width = screenshot.shape[:2]
            
            # 定义可能的对话框区域（屏幕中央附近）
            center_x, center_y = width // 2, height // 2
            
            # 检查几个可能的对话框位置
            possible_regions = [
                (center_x - 200, center_y - 100, 400, 200),  # 中央小对话框
                (center_x - 300, center_y - 150, 600, 300),  # 中央中等对话框
                (center_x - 150, center_y - 75, 300, 150),   # 中央更小对话框
            ]
            
            for x, y, w, h in possible_regions:
                # 确保区域在屏幕范围内
                x = max(0, min(x, width - w))
                y = max(0, min(y, height - h))
                
                region = screenshot[y:y+h, x:x+w]
                
                if self._has_dialog_features(region):
                    return {
                        'detected': True,
                        'method': 'template_matching',
                        'region': (x, y, w, h),
                        'center_x': x + w // 2,
                        'center_y': y + h // 2
                    }
            
            return None
            
        except Exception as e:
            self.logger.error(f"模板匹配检测失败: {e}")
            return None
    
    def _has_dialog_features(self, region: np.ndarray) -> bool:
        """检查区域是否具有对话框特征"""
        try:
            if region.size == 0:
                return False
            
            # 检查颜色特征（对话框通常是浅色背景）
            gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
            avg_brightness = np.mean(gray)
            
            # 对话框通常比较亮
            if avg_brightness < 150:
                return False
            
            # 检查是否有文字特征（边缘较多）
            edges = cv2.Canny(gray, 50, 150)
            edge_ratio = np.sum(edges > 0) / edges.size
            
            # 对话框应该有一定的边缘特征（文字、按钮边框等）
            if 0.01 < edge_ratio < 0.15:
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查对话框特征失败: {e}")
            return False
    
    def _load_calibrated_positions(self) -> Dict:
        """加载校准的坐标配置"""
        try:
            config_file = "smart_coordinates_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('button_positions', {})
            return {}
        except Exception as e:
            self.logger.error(f"加载校准坐标失败: {e}")
            return {}
    
    def _click_calibrated_confirm_button(self, wait_for_dialog: bool = True, max_wait: float = 3.0) -> bool:
        """使用校准的坐标点击确认按钮"""
        try:
            confirm_button = self.calibrated_positions.get('confirm_button')
            if not confirm_button:
                self.logger.info("未找到校准的确认按钮坐标")
                return False
            
            # 获取当前屏幕尺寸
            screen_width, screen_height = pyautogui.size()
            
            # 计算绝对坐标
            abs_x = int(confirm_button['x'] * screen_width)
            abs_y = int(confirm_button['y'] * screen_height)
            
            self.logger.info(f"使用校准坐标等待确认按钮: ({abs_x}, {abs_y})")
            
            if wait_for_dialog:
                # 智能等待对话框出现
                start_time = time.time()
                while time.time() - start_time < max_wait:
                    if self._has_confirm_dialog_at_position(abs_x, abs_y):
                        self.logger.info(f"✅ 检测到确认对话框，等待时间: {time.time() - start_time:.1f}秒")
                        # 稍等一下确保对话框完全加载
                        time.sleep(0.2)
                        pyautogui.click(abs_x, abs_y)
                        time.sleep(0.3)
                        self.logger.info("✅ 使用校准坐标成功点击确认按钮")
                        return True
                    time.sleep(0.1)  # 每100ms检查一次
                
                self.logger.info(f"⏰ 等待{max_wait}秒后仍未检测到确认对话框")
                return False
            else:
                # 直接点击（不等待）
                if self._has_confirm_dialog_at_position(abs_x, abs_y):
                    pyautogui.click(abs_x, abs_y)
                    time.sleep(0.3)
                    self.logger.info("✅ 使用校准坐标成功点击确认按钮")
                    return True
                else:
                    self.logger.info("校准位置附近未检测到确认对话框")
                    return False
                
        except Exception as e:
            self.logger.error(f"使用校准坐标点击失败: {e}")
            return False
    
    def _has_confirm_dialog_at_position(self, x: int, y: int, tolerance: int = 100) -> bool:
        """检查指定位置附近是否有确认对话框"""
        try:
            # 获取屏幕截图
            screenshot = pyautogui.screenshot()
            screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # 定义检查区域（以指定位置为中心）
            check_x = max(0, x - tolerance)
            check_y = max(0, y - tolerance) 
            check_w = min(screenshot.shape[1] - check_x, tolerance * 2)
            check_h = min(screenshot.shape[0] - check_y, tolerance * 2)
            
            check_region = screenshot[check_y:check_y+check_h, check_x:check_x+check_w]
            
            # 检查是否具有对话框特征
            return self._has_dialog_features(check_region)
            
        except Exception as e:
            self.logger.error(f"检查确认对话框位置失败: {e}")
            return False
    
    def find_ok_button(self, dialog_region: Tuple[int, int, int, int]) -> Optional[Tuple[int, int]]:
        """在对话框区域内查找确定按钮"""
        try:
            x, y, w, h = dialog_region
            
            # 确定按钮通常在对话框的右下部分
            # 基于您的截图，确定按钮在右侧
            button_region_x = x + int(w * 0.5)  # 右半部分
            button_region_y = y + int(h * 0.6)  # 下半部分
            button_w = int(w * 0.4)
            button_h = int(h * 0.3)
            
            # 确定按钮可能的位置（基于您的截图估算）
            possible_buttons = [
                (button_region_x + button_w // 3, button_region_y + button_h // 2),  # 右侧中央
                (x + int(w * 0.65), y + int(h * 0.75)),  # 右下
                (x + int(w * 0.6), y + int(h * 0.7)),   # 稍左一点
            ]
            
            return possible_buttons[0]  # 返回最可能的位置
            
        except Exception as e:
            self.logger.error(f"查找确定按钮失败: {e}")
            return None
    
    def auto_confirm(self, delay: float = 0.5, wait_for_dialog: bool = True, max_wait: float = 3.0) -> bool:
        """自动确认对话框"""
        try:
            self.logger.info("开始自动确认检测...")
            
            # 优先使用校准的坐标（带智能等待）
            if self._click_calibrated_confirm_button(wait_for_dialog=wait_for_dialog, max_wait=max_wait):
                return True
            
            # 如果校准坐标失败，使用检测方法
            if wait_for_dialog:
                # 等待模式：持续检测直到找到对话框
                start_time = time.time()
                while time.time() - start_time < max_wait:
                    dialog_info = self.detect_confirm_dialog()
                    if dialog_info.get('detected', False):
                        self.logger.info(f"检测到确认对话框: {dialog_info}")
                        break
                    time.sleep(0.1)
                else:
                    self.logger.info(f"⏰ 等待{max_wait}秒后仍未检测到确认对话框")
                    return False
            else:
                # 即时模式：只检测一次
                dialog_info = self.detect_confirm_dialog()
                if not dialog_info.get('detected', False):
                    self.logger.info("未检测到确认对话框")
                    return False
                self.logger.info(f"检测到确认对话框: {dialog_info}")
            
            # 等待一小段时间确保对话框完全显示
            time.sleep(delay)
            
            # 根据检测方法确定点击位置
            if dialog_info.get('method') == 'window_api':
                # 使用窗口中心附近的确定按钮位置
                click_x = dialog_info['center_x'] + 50  # 稍微偏右，通常确定按钮在右侧
                click_y = dialog_info['center_y'] + 20  # 稍微偏下
            else:
                # 查找确定按钮位置
                region = dialog_info.get('region')
                if region:
                    button_pos = self.find_ok_button(region)
                    if button_pos:
                        click_x, click_y = button_pos
                    else:
                        # 使用中心偏右下的位置
                        click_x = dialog_info['center_x'] + 50
                        click_y = dialog_info['center_y'] + 20
                else:
                    # 使用检测到的中心位置
                    click_x = dialog_info['center_x'] + 50
                    click_y = dialog_info['center_y'] + 20
            
            # 执行点击
            self.logger.info(f"点击确定按钮位置: ({click_x}, {click_y})")
            
            # 保存点击前的截图
            before_click = pyautogui.screenshot()
            cv2.imwrite(f"before_click_{int(time.time())}.png", 
                       cv2.cvtColor(np.array(before_click), cv2.COLOR_RGB2BGR))
            
            # 点击确定按钮
            pyautogui.click(click_x, click_y)
            
            # 等待响应
            time.sleep(0.3)
            
            # 保存点击后的截图
            after_click = pyautogui.screenshot()
            cv2.imwrite(f"after_click_{int(time.time())}.png", 
                       cv2.cvtColor(np.array(after_click), cv2.COLOR_RGB2BGR))
            
            self.logger.info("✅ 自动确认完成")
            return True
            
        except Exception as e:
            self.logger.error(f"自动确认失败: {e}")
            return False
    
    def wait_and_confirm(self, max_wait_time: float = 5.0) -> bool:
        """等待确认对话框出现并自动确认 - 针对延迟出现的对话框优化"""
        try:
            self.logger.info(f"智能等待确认对话框 (最大等待时间: {max_wait_time}秒)")
            
            # 使用内置的智能等待机制
            return self.auto_confirm(wait_for_dialog=True, max_wait=max_wait_time)
            
        except Exception as e:
            self.logger.error(f"等待确认失败: {e}")
            return False

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 创建处理器
    handler = AutoConfirmHandler()
    
    print("自动确认处理器已启动")
    print("请在5秒内触发确认对话框...")
    
    # 等待并自动确认
    if handler.wait_and_confirm(max_wait_time=10.0):
        print("✅ 自动确认成功")
    else:
        print("❌ 未检测到确认对话框")