@echo off
chcp 65001
echo 智能交易系统环境修复脚本
echo ================================

echo 1. 检查Python环境...
python --version
if errorlevel 1 (
    echo 错误: 未找到Python
    echo 请确保Python已正确安装并添加到PATH
    pause
    exit /b 1
)

echo 2. 升级pip...
python -m pip install --upgrade pip --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org

echo 3. 安装核心依赖包...
python -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org PyQt5==5.15.9
python -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org numpy==1.24.3
python -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org opencv-python==********
python -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org pyautogui==0.9.54
python -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org pytesseract==0.3.10
python -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org psutil==5.9.5
python -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org Pillow==10.0.0

echo 4. 安装其他依赖...
python -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org pywin32==306
python -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org pygetwindow==0.0.9

echo 5. 验证安装...
python -c "import PyQt5; print('PyQt5: OK')"
python -c "import cv2; print('OpenCV: OK')"
python -c "import pyautogui; print('PyAutoGUI: OK')"
python -c "import numpy; print('NumPy: OK')"

echo 6. 环境修复完成！
echo 现在可以运行: python smart_trading.py
pause
