#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证坐标配置
"""

import json
import os

def verify_coordinates():
    """验证坐标配置"""
    print("🔍 验证坐标配置...")
    
    # 读取配置文件
    config_file = 'smart_coordinates_config.json'
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    if 'button_positions' not in config:
        print("❌ 配置文件中没有按钮位置信息")
        return False
    
    buttons = config['button_positions']
    # 假设屏幕分辨率 (可以从窗口信息获取)
    window_info = config.get('window_info', {})
    screen_width = window_info.get('rect', {}).get('width', 2582)
    screen_height = window_info.get('rect', {}).get('height', 1550)
    
    print(f"📏 当前屏幕分辨率: {screen_width} x {screen_height}")
    print(f"📍 已配置按钮数量: {len(buttons)}")
    print("-" * 60)
    
    # 验证每个按钮
    for button_key, button_data in buttons.items():
        name = button_data.get('name', button_key)
        rel_x = button_data.get('x', 0)
        rel_y = button_data.get('y', 0)
        
        # 计算绝对坐标
        abs_x = int(screen_width * rel_x)
        abs_y = int(screen_height * rel_y)
        
        # 获取校准时的绝对坐标（如果有）
        calibrated_abs = button_data.get('calibrated_absolute', {})
        cal_x = calibrated_abs.get('x', 0)
        cal_y = calibrated_abs.get('y', 0)
        
        print(f"🎯 {name} ({button_key})")
        print(f"   相对坐标: ({rel_x:.6f}, {rel_y:.6f})")
        print(f"   计算坐标: ({abs_x}, {abs_y})")
        if cal_x and cal_y:
            print(f"   校准坐标: ({cal_x}, {cal_y})")
            # 计算差异
            diff_x = abs(abs_x - cal_x)
            diff_y = abs(abs_y - cal_y)
            if diff_x > 5 or diff_y > 5:
                print(f"   ⚠️  坐标差异较大: Δx={diff_x}, Δy={diff_y}")
            else:
                print(f"   ✅ 坐标匹配良好: Δx={diff_x}, Δy={diff_y}")
        
        # 检查坐标是否在屏幕范围内
        if 0 <= abs_x <= screen_width and 0 <= abs_y <= screen_height:
            print(f"   ✅ 坐标在屏幕范围内")
        else:
            print(f"   ❌ 坐标超出屏幕范围")
        
        print()
    
    print("-" * 60)
    
    # 检查必要按钮
    required_buttons = [
        'buy_mode_button',
        'sell_mode_button', 
        'buy_order_button',
        'sell_order_button',
        'price_input',
        'quantity_input'
    ]
    
    missing_buttons = []
    for button in required_buttons:
        if button not in buttons:
            missing_buttons.append(button)
    
    if missing_buttons:
        print(f"❌ 缺少必要按钮: {', '.join(missing_buttons)}")
        return False
    else:
        print("✅ 所有必要按钮都已配置")
    
    # 检查可选按钮
    optional_buttons = [
        'confirm_button',
        'transfer_out_button',
        'order_mode_button'
    ]
    
    missing_optional = []
    for button in optional_buttons:
        if button not in buttons:
            missing_optional.append(button)
    
    if missing_optional:
        print(f"⚠️  缺少可选按钮: {', '.join(missing_optional)}")
    else:
        print("✅ 所有可选按钮都已配置")
    
    print("\n🎉 坐标验证完成！")
    return True

def show_button_mapping():
    """显示按钮映射表"""
    print("\n📋 按钮映射表：")
    print("-" * 40)
    
    button_mapping = {
        'buy_mode_button': '买入模式按钮 (左侧红色的"买")',
        'sell_mode_button': '卖出模式按钮 (左侧绿色的"卖")',
        'buy_order_button': '买入订立按钮 (底部)',
        'sell_order_button': '卖出订立按钮 (底部)',
        'price_input': '价格输入框',
        'quantity_input': '数量输入框',
        'transfer_out_button': '转出按钮',
        'confirm_button': '确认按钮 (弹出对话框)',
        'order_mode_button': '订立模式按钮'
    }
    
    for key, desc in button_mapping.items():
        print(f"{key:<20} : {desc}")

def main():
    print("=" * 60)
    print("智能交易系统 - 坐标配置验证")
    print("=" * 60)
    
    # 验证坐标
    if verify_coordinates():
        print("✅ 坐标配置验证通过")
    else:
        print("❌ 坐标配置验证失败")
    
    # 显示按钮映射
    show_button_mapping()
    
    print("\n" + "=" * 60)
    print("验证完成")
    print("=" * 60)

if __name__ == "__main__":
    main()