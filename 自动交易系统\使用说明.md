# 智能交易系统使用说明

## 🚀 快速开始

### 方法1：双击启动（推荐）
```
双击 "启动.bat" 文件
```

### 方法2：命令行启动
```bash
python smart_trading.py
```

### 方法3：使用启动器
```bash
python 启动系统.py
```

## 📋 系统要求

- **Python版本**: 3.7 或更高
- **操作系统**: Windows 10/11
- **内存**: 至少 4GB RAM
- **显示器**: 1920x1080 或更高分辨率

## 📦 依赖包

系统需要以下Python包：

- **PyQt5** - GUI界面
- **opencv-python** - 图像处理
- **numpy** - 数值计算
- **pyautogui** - 自动化操作
- **Pillow** - 图像处理
- **pytesseract** - OCR文字识别（可选）
- **psutil** - 系统监控（可选）

## 🔧 安装依赖

如果系统提示缺少依赖，请运行：

```bash
# 方法1：自动安装
python install_dependencies.py

# 方法2：手动安装
pip install PyQt5 opencv-python numpy pyautogui Pillow

# 方法3：使用国内镜像（网络慢时）
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ PyQt5 opencv-python numpy pyautogui Pillow
```

## ⚙️ 配置文件

- **config/trading_config.json** - 主要配置文件
- **smart_coordinates_config.json** - 坐标配置文件

首次运行时，系统会自动创建默认配置。

## 🎯 主要功能

1. **智能检测** - 自动检测交易信号
2. **风险控制** - 内置止损和风险管理
3. **自动交易** - 自动执行买卖操作
4. **实时监控** - 实时显示交易状态
5. **历史记录** - 保存交易历史

## 🚨 注意事项

1. **首次使用前请仔细阅读风险提示**
2. **建议先在模拟环境中测试**
3. **确保交易客户端正常运行**
4. **定期备份配置和数据**

## 🔍 故障排除

### 常见问题

**Q: 提示缺少依赖包？**
A: 运行 `python install_dependencies.py` 自动安装

**Q: 无法检测到交易客户端？**
A: 确保客户端已启动并在前台显示

**Q: 坐标定位不准确？**
A: 运行坐标校准工具重新校准

**Q: 系统运行缓慢？**
A: 检查系统资源使用情况，关闭不必要的程序

### 获取帮助

如果遇到问题：

1. 查看系统日志文件
2. 检查配置文件格式
3. 重新安装依赖包
4. 重启系统和交易客户端

## 📁 文件结构

```
自动交易系统/
├── smart_trading.py          # 主程序
├── 启动.bat                  # 快速启动脚本
├── 启动系统.py               # 启动器
├── install_dependencies.py   # 依赖安装脚本
├── config/                   # 配置文件目录
├── core/                     # 核心模块
├── utils/                    # 工具模块
└── 使用说明.md               # 本文件
```

## 🔄 更新日志

### v1.0 (当前版本)
- ✅ 修复了重复配置管理器问题
- ✅ 改进了依赖检查机制
- ✅ 添加了友好的启动界面
- ✅ 优化了错误处理

## ⚠️ 免责声明

本软件仅供学习和研究使用。使用本软件进行实际交易的风险由用户自行承担。开发者不对任何交易损失负责。

---

**祝您使用愉快！** 🎉
