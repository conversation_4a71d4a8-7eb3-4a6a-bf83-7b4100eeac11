#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动价格区域选择演示
演示如何使用手动区域选择替代OCR
"""

import logging
import time
import os
import sys

# 添加源码路径
src_path = os.path.join(os.path.dirname(__file__), 'src')
if src_path not in sys.path:
    sys.path.append(src_path)

tools_path = os.path.join(os.path.dirname(__file__), 'tools')
if tools_path not in sys.path:
    sys.path.append(tools_path)

def main():
    """主演示函数"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    
    print("=" * 60)
    print("景陶易购手动价格区域选择演示")
    print("=" * 60)
    print()
    
    try:
        from trading.real_price_detector import RealPriceDetector
        
        print("选择价格检测模式：")
        print("1. OCR模式（自动识别，可能不准确）")
        print("2. 手动区域选择模式（用户选择区域，更准确）")
        print()
        
        choice = input("请选择模式 (1 或 2): ").strip()
        
        if choice == '2':
            print("\n🎯 启动手动区域选择模式...")
            print("请按照提示在屏幕上选择价格区域")
            
            # 创建手动选择模式的检测器
            detector = RealPriceDetector(use_manual_selection=True)
            
            # 检查是否需要设置区域
            if not detector.manual_selector.selected_regions:
                print("\n📋 需要先设置价格区域")
                input("请确保景陶易购客户端已打开，然后按回车继续...")
                
                success = detector.setup_manual_regions()
                if not success:
                    print("❌ 区域设置失败，退出演示")
                    return
                
                print("✅ 区域设置完成")
            else:
                print(f"✅ 已加载 {len(detector.manual_selector.selected_regions)} 个价格区域")
            
            print("\n🔄 开始价格监测演示...")
            
            # 监测价格
            for i in range(10):
                try:
                    print(f"\n--- 第 {i+1} 次价格检测 ---")
                    
                    # 获取所有价格
                    prices = detector.get_multiple_prices()
                    
                    if prices:
                        for region_name, price in prices.items():
                            if price:
                                print(f"💰 {region_name}: {price}")
                            else:
                                print(f"❌ {region_name}: 获取失败")
                    else:
                        print("⚠️ 未获取到任何价格")
                    
                    time.sleep(2)
                    
                except KeyboardInterrupt:
                    print("\n\n⏹️ 用户中断演示")
                    break
                except Exception as e:
                    print(f"❌ 价格检测出错: {e}")
                    
        elif choice == '1':
            print("\n🔍 启动OCR模式...")
            
            # 创建OCR模式的检测器
            detector = RealPriceDetector(use_manual_selection=False)
            
            print("🔄 开始价格监测演示...")
            
            # 监测价格
            for i in range(5):
                try:
                    print(f"\n--- 第 {i+1} 次价格检测 ---")
                    
                    # 获取主要价格
                    main_price = detector.get_real_price('main_price')
                    current_price = detector.get_real_price('current_price')
                    
                    if main_price:
                        print(f"💰 主要价格: {main_price}")
                    else:
                        print("❌ 主要价格: 获取失败")
                    
                    if current_price:
                        print(f"💰 当前价格: {current_price}")
                    else:
                        print("❌ 当前价格: 获取失败")
                    
                    time.sleep(3)
                    
                except KeyboardInterrupt:
                    print("\n\n⏹️ 用户中断演示")
                    break
                except Exception as e:
                    print(f"❌ 价格检测出错: {e}")
        else:
            print("❌ 无效选择")
            return
            
        print("\n✅ 演示完成")
        
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        print("❌ 系统初始化失败，请检查依赖项")
        
    except Exception as e:
        logger.error(f"演示出错: {e}")
        print(f"❌ 演示过程中出错: {e}")

if __name__ == "__main__":
    main()