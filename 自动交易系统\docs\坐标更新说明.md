# 智能交易系统 - 坐标更新说明

## 📍 更新内容

### 1. 坐标加载优化
- ✅ 优先使用 `calibrated_absolute` 绝对坐标
- ✅ 自动计算相对坐标，适应不同窗口尺寸
- ✅ 加载配置时显示详细的坐标信息
- ✅ 内置精确的默认坐标配置

### 2. 坐标验证功能
- ✅ 启动时自动验证坐标配置有效性
- ✅ 检查坐标是否在合理范围内 (0-1)
- ✅ 验证必需按钮是否都有配置

### 3. 窗口尺寸对比
- ✅ 对比当前窗口与配置文件中的窗口尺寸
- ✅ 检测窗口尺寸变化，提醒是否需要重新校准
- ✅ 在尺寸变化较大时发出警告

### 4. 坐标测试功能
- ✅ 新增坐标测试方法，验证计算出的坐标是否准确
- ✅ 检查坐标是否在窗口范围内
- ✅ 在校准完成后自动测试坐标
- ✅ 在执行交易前验证关键按钮坐标

### 5. UI界面增强
- ✅ 新增"测试坐标"按钮
- ✅ 一键测试所有关键按钮位置
- ✅ 友好的测试结果提示

### 6. 日志信息增强
- ✅ 详细的坐标计算日志
- ✅ 更清晰的状态标识（✅❌⚠️🎯📍等）
- ✅ 分步骤的操作说明

## 📋 当前支持的坐标配置

| 按钮名称 | 配置键名 | 描述 |
|---------|---------|------|
| 买入模式按钮 | `buy_mode_button` | 左侧红色的'买'按钮 |
| 卖出模式按钮 | `sell_mode_button` | 左侧绿色的'卖'按钮 |
| 买入订立按钮 | `buy_order_button` | 底部的'买入订立'按钮 |
| 卖出订立按钮 | `sell_order_button` | 底部的'卖出订立'按钮 |
| 价格输入框 | `price_input` | 买价输入框 |
| 数量输入框 | `quantity_input` | 买量输入框 |
| 确认按钮 | `confirm_button` | 弹出对话框中的'确定'按钮 |
| 转出按钮 | `transfer_out_button` | 转出按钮 |
| 订立模式按钮 | `order_mode_button` | 订立模式按钮 |

## 🔧 配置文件格式

```json
{
  "window_info": {
    "title": "景陶易购窗口标题",
    "rect": {
      "left": -11,
      "top": -11,
      "width": 2582,
      "height": 1550,
      "right": 2571,
      "bottom": 1539
    }
  },
  "button_positions": {
    "buy_mode_button": {
      "name": "买入模式按钮",
      "x": 0.01395348837209302,
      "y": 0.7064516129032258,
      "calibrated_absolute": {
        "x": 36,
        "y": 1095
      },
      "description": "左侧红色的'买'按钮",
      "last_calibrated": "2025-01-04 14:35:26"
    }
  }
}
```

## 🚀 使用说明

### 1. 启动前准备
1. 确保景陶易购客户端已打开
2. 确保配置文件 `smart_coordinates_config.json` 在程序目录下
3. 启动智能交易系统

### 2. 坐标验证
1. 程序启动时会自动验证坐标配置
2. 可以随时点击"测试坐标"按钮手动测试
3. 注意查看日志中的坐标验证结果

### 3. 问题排查
- 如果坐标测试失败，检查：
  - 景陶易购客户端是否正常打开
  - 窗口是否被遮挡或最小化
  - 窗口尺寸是否与配置文件中的差异过大
  - 配置文件是否完整

### 4. 重新校准
- 如果窗口尺寸变化较大，建议重新校准
- 删除配置文件让程序重新生成
- 或使用图像识别重新校准按钮位置

## ⚡ 新功能特性

1. **智能坐标适配**：自动适应不同窗口尺寸
2. **实时验证**：交易前验证坐标准确性
3. **详细日志**：提供完整的坐标计算过程
4. **友好提示**：清晰的状态标识和错误提示
5. **一键测试**：方便的坐标测试功能

## 📝 注意事项

1. 确保景陶易购客户端窗口可见且未被遮挡
2. 避免在交易过程中移动或调整客户端窗口
3. 定期检查坐标配置是否需要更新
4. 注意查看日志中的警告和错误信息

---

**版本**：v2.0  
**更新日期**：2025-01-04  
**主要改进**：坐标系统全面升级，提高定位准确性