@echo off
chcp 65001
echo 智能交易系统启动器
echo ===================

echo 检查环境...
python -c "import PyQt5, cv2, pyautogui, numpy" >nul 2>&1
if errorlevel 1 (
    echo 依赖包未安装，正在安装...
    echo 使用清华镜像源安装依赖包...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ -r requirements_core.txt
    if errorlevel 1 (
        echo 依赖包安装失败，请检查网络连接
        pause
        exit /b 1
    )
)

echo 环境检查完成！
echo.
echo 启动智能交易系统...
echo 注意：请确保景陶易购客户端已经打开
echo.

python smart_trading.py

if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查：
    echo 1. 景陶易购客户端是否已打开
    echo 2. Python依赖包是否正确安装
    echo 3. 查看上方错误信息
    echo.
    pause
) else (
    echo 程序正常退出
)
