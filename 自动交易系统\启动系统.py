#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能交易系统启动器
简化启动流程，自动检查和修复常见问题
"""

import os
import sys
import subprocess

def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("🚀 智能交易系统启动器")
    print("=" * 60)

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 7:
        print("✅ Python版本符合要求")
        return True
    else:
        print("❌ Python版本过低，需要Python 3.7+")
        return False

def check_dependencies():
    """检查依赖"""
    print("\n检查依赖...")
    
    required_packages = [
        ("PyQt5", "PyQt5.QtWidgets"),
        ("OpenCV", "cv2"),
        ("NumPy", "numpy"),
        ("PyAutoG<PERSON>", "pyautogui"),
        ("Pillow", "PIL")
    ]
    
    missing = []
    
    for name, module in required_packages:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name}")
            missing.append(name)
    
    return missing

def install_missing_dependencies(missing_deps):
    """安装缺失的依赖"""
    if not missing_deps:
        return True
    
    print(f"\n发现缺失依赖: {', '.join(missing_deps)}")
    
    choice = input("\n是否自动安装? (y/n): ").lower().strip()
    
    if choice == 'y':
        print("\n正在安装依赖...")
        
        # 包名映射
        package_map = {
            "PyQt5": "PyQt5",
            "OpenCV": "opencv-python",
            "NumPy": "numpy",
            "PyAutoGUI": "pyautogui",
            "Pillow": "Pillow"
        }
        
        for dep in missing_deps:
            package_name = package_map.get(dep, dep)
            print(f"安装 {package_name}...")
            
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", package_name
                ], capture_output=True, text=True, timeout=120)
                
                if result.returncode == 0:
                    print(f"✅ {package_name} 安装成功")
                else:
                    print(f"❌ {package_name} 安装失败")
                    print(f"错误: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print(f"⏰ {package_name} 安装超时")
            except Exception as e:
                print(f"❌ {package_name} 安装异常: {e}")
        
        # 重新检查
        print("\n重新检查依赖...")
        missing_after = check_dependencies()
        return len(missing_after) == 0
    
    else:
        print("\n手动安装命令:")
        package_map = {
            "PyQt5": "PyQt5",
            "OpenCV": "opencv-python", 
            "NumPy": "numpy",
            "PyAutoGUI": "pyautogui",
            "Pillow": "Pillow"
        }
        
        packages = [package_map.get(dep, dep) for dep in missing_deps]
        print(f"pip install {' '.join(packages)}")
        return False

def check_config_files():
    """检查配置文件"""
    print("\n检查配置文件...")
    
    config_files = [
        "config/trading_config.json",
        "smart_coordinates_config.json"
    ]
    
    missing_configs = []
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ {config_file}")
        else:
            print(f"⚠️ {config_file} 不存在")
            missing_configs.append(config_file)
    
    if missing_configs:
        print("\n⚠️ 部分配置文件缺失，系统将使用默认配置")
    
    return True

def start_trading_system():
    """启动交易系统"""
    print("\n🚀 启动交易系统...")
    
    try:
        # 直接运行主程序
        subprocess.run([sys.executable, "smart_trading.py"], check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败，退出码: {e.returncode}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
        return True
    except Exception as e:
        print(f"❌ 启动异常: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        input("\n按回车键退出...")
        return
    
    # 检查依赖
    missing_deps = check_dependencies()
    
    if missing_deps:
        if not install_missing_dependencies(missing_deps):
            print("\n❌ 依赖安装失败或被跳过")
            input("按回车键退出...")
            return
    
    # 检查配置文件
    check_config_files()
    
    # 启动系统
    print("\n" + "=" * 60)
    success = start_trading_system()
    
    if success:
        print("\n✅ 系统运行完成")
    else:
        print("\n❌ 系统运行失败")
        print("\n💡 故障排除建议:")
        print("1. 检查是否有其他Python进程占用")
        print("2. 重新安装依赖: python install_dependencies.py")
        print("3. 查看错误日志")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
