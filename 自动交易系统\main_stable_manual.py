#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
景陶易购智能交易系统 v2.0 (稳定版) - 支持手动价格选择
"""

import sys
import os
import logging
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))
sys.path.insert(0, str(project_root / 'config'))
sys.path.insert(0, str(project_root / 'utils'))
sys.path.insert(0, str(project_root / 'tools'))

def check_dependencies():
    """检查系统依赖"""
    print("🔍 检查系统依赖...")
    
    required_packages = [
        ('PyQt5', 'PyQt5'),
        ('numpy', 'numpy'),
        ('opencv-python', 'cv2'),
        ('Pillow', 'PIL'),
        ('pyautogui', 'pyautogui')
    ]
    
    optional_packages = [
        ('pytesseract', 'pytesseract')
    ]
    
    missing_packages = []
    
    # 检查必需包
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name} 已安装")
        except ImportError:
            print(f"❌ {package_name} 未安装")
            missing_packages.append(package_name)
    
    # 检查可选包
    for package_name, import_name in optional_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name} 已安装 (可选)")
        except ImportError:
            print(f"⚠️ {package_name} 未安装 (可选)")
    
    if missing_packages:
        print(f"\n❌ 缺少必需依赖: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("\n✅ 依赖检查通过")
    return True

def setup_logging():
    """设置日志"""
    log_dir = project_root / 'logs'
    log_dir.mkdir(exist_ok=True)
    
    log_file = log_dir / f'trading_log_{time.strftime("%Y%m%d_%H%M%S")}.txt'
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

def main():
    """主函数"""
    print("🐍 Python版本:", sys.version.split()[0])
    print("🚀 景陶易购智能交易系统 v2.0 (稳定版) - 手动价格选择")
    print("=" * 55)
    
    # 检查依赖
    if not check_dependencies():
        input("\n按回车键退出...")
        return 1
    
    # 设置日志
    logger = setup_logging()
    logger.info("系统启动")
    
    try:
        # 检查启动模式
        mode = 'gui'  # 默认GUI模式
        if len(sys.argv) > 1:
            mode = sys.argv[1].lower()
        
        print(f"🎯 启动模式: {mode}")
        
        if mode == 'gui':
            print("🖥️ 启动GUI模式...")
            from PyQt5.QtWidgets import QApplication, QMessageBox
            from PyQt5.QtCore import Qt
            
            # 创建Qt应用
            app = QApplication(sys.argv)
            app.setAttribute(Qt.AA_EnableHighDpiScaling)
            print("✅ Qt应用创建成功")
            
            try:
                # 导入GUI模块
                from trading.trading_gui import TradingGUI
                print("✅ GUI模块导入成功")
                
                # 询问用户是否使用手动价格选择
                reply = QMessageBox.question(
                    None,
                    '价格检测模式选择',
                    '选择价格检测模式：\n\n'
                    '是(Yes): 使用手动价格区域选择 (推荐)\n'
                    '否(No): 使用OCR自动识别\n\n'
                    '手动价格选择更准确稳定，但需要先设置价格区域。',
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                
                use_manual_selection = (reply == QMessageBox.Yes)
                
                if use_manual_selection:
                    # 检查是否已设置价格区域
                    region_config_file = project_root / 'config' / 'price_regions_manual.json'
                    if not region_config_file.exists():
                        setup_reply = QMessageBox.question(
                            None,
                            '需要设置价格区域',
                            '检测到您还没有设置价格区域。\n\n'
                            '是否现在设置价格区域？\n'
                            '(设置完成后需要重新启动系统)',
                            QMessageBox.Yes | QMessageBox.No,
                            QMessageBox.Yes
                        )
                        
                        if setup_reply == QMessageBox.Yes:
                            print("🔧 启动价格区域设置工具...")
                            try:
                                from tools.manual_price_setup import ManualPriceSetupGUI
                                setup_app = ManualPriceSetupGUI()
                                setup_app.run()
                                
                                QMessageBox.information(
                                    None,
                                    '设置完成',
                                    '价格区域设置完成！\n请重新启动交易系统。'
                                )
                                return 0
                            except Exception as e:
                                logger.error(f"价格区域设置失败: {e}")
                                QMessageBox.critical(
                                    None,
                                    '设置失败',
                                    f'价格区域设置失败: {e}\n\n将使用OCR模式。'
                                )
                                use_manual_selection = False
                        else:
                            use_manual_selection = False
                
                # 更新配置文件
                try:
                    from config.config_manager import EnhancedConfigManager
                    config_manager = EnhancedConfigManager()
                    
                    # 创建新的配置
                    current_config = config_manager.config.copy()
                    if 'price_detection' not in current_config:
                        current_config['price_detection'] = {}
                    
                    current_config['price_detection']['use_manual_price_selection'] = use_manual_selection
                    
                    # 保存配置
                    config_manager.config = current_config
                    config_manager.save_config()
                    
                    mode_text = "手动价格选择" if use_manual_selection else "OCR自动识别"
                    print(f"✅ 配置已更新 - 价格检测模式: {mode_text}")
                    
                except Exception as e:
                    logger.warning(f"配置更新失败: {e}")
                
                # 创建GUI窗口
                window = TradingGUI()
                window.show()
                print("✅ GUI窗口已显示")
                print("🎯 系统就绪，请在界面中操作")
                
                # 运行应用
                sys.exit(app.exec_())
                
            except Exception as e:
                logger.error(f"GUI启动失败: {e}")
                print(f"❌ GUI启动失败: {e}")
                return 1
                
        else:
            print("❌ 不支持的启动模式")
            return 1
    
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断程序")
        logger.info("用户中断程序")
        return 0
        
    except Exception as e:
        logger.error(f"系统启动失败: {e}")
        print(f"❌ 系统启动失败: {e}")
        return 1
    
    finally:
        print("\n" + "=" * 55)
        print("✅ 程序正常结束")
        input("\n按回车键退出...")

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)