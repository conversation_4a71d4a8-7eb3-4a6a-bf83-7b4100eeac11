#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版配置管理器
支持动态配置更新、多环境配置、配置验证等功能
"""

import json
import os
import logging
import shutil
from typing import Dict, Any, Optional, List
from datetime import datetime
import threading

class EnhancedConfigManager:
    """增强版配置管理器"""
    
    def __init__(self, config_file: str = "config/trading_config.json"):
        self.config_file = config_file
        self.config_dir = os.path.dirname(config_file)
        self.config = {}
        self.default_config = {}
        self.config_lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
        # 确保配置目录存在
        if self.config_dir and not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
        
        # 加载配置
        self.load_config()
        
        self.logger.info("增强版配置管理器初始化完成")
    
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            with self.config_lock:
                if os.path.exists(self.config_file):
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        self.config = json.load(f)
                    self.logger.info(f"配置文件加载成功: {self.config_file}")
                else:
                    self.logger.warning(f"配置文件不存在，使用默认配置: {self.config_file}")
                    self.config = self.get_default_config()
                    self.save_config()
                
                # 验证配置
                self.validate_config()
                return True
                
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self.config = self.get_default_config()
            return False
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            with self.config_lock:
                # 备份现有配置
                if os.path.exists(self.config_file):
                    backup_file = f"{self.config_file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    shutil.copy2(self.config_file, backup_file)
                
                # 保存新配置
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
                
                self.logger.info("配置文件保存成功")
                return True
                
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "system": {
                "version": "2.0.0",
                "environment": "production",
                "debug_mode": False,
                "log_level": "INFO"
            },
            "trading": {
                "parameters": {
                    "trade_cooldown": 10,
                    "min_angle_change": 2.0,
                    "fixed_price": 1383,
                    "default_quantity": 1
                },
                "profit_loss": {
                    "profit_threshold": 1.0,
                    "loss_threshold": -3.0,
                    "enable_auto_transfer": True
                },
                "risk_management": {
                    "max_daily_trades": 100,
                    "max_daily_loss": 500,
                    "enable_circuit_breaker": True
                }
            },
            "detection": {
                "yellow_line": {
                    "color_ranges": [
                        {
                            "name": "bright_yellow",
                            "lower": [15, 100, 100],
                            "upper": [35, 255, 255]
                        }
                    ],
                    "min_area": 50,
                    "data_points_used": 2
                },
                "profit_loss": {
                    "region": {
                        "x_ratio": 0.6,
                        "y_ratio": 0.6,
                        "width_ratio": 0.35,
                        "height_ratio": 0.3
                    }
                }
            },
            "ui": {
                "window": {
                    "width": 1200,
                    "height": 800
                },
                "theme": {
                    "name": "dark"
                }
            }
        }
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        try:
            # 验证必需的配置项
            required_keys = [
                "system",
                "trading.parameters",
                "trading.profit_loss",
                "detection.yellow_line",
                "detection.profit_loss"
            ]
            
            for key in required_keys:
                if not self.get(key):
                    self.logger.warning(f"缺少必需的配置项: {key}")
                    return False
            
            # 验证数值范围
            trade_cooldown = self.get("trading.parameters.trade_cooldown", 10)
            if not (1 <= trade_cooldown <= 300):
                self.logger.warning(f"交易冷却时间超出范围: {trade_cooldown}")
                self.set("trading.parameters.trade_cooldown", 10)
            
            profit_threshold = self.get("trading.profit_loss.profit_threshold", 1.0)
            if not (0.1 <= profit_threshold <= 100):
                self.logger.warning(f"止盈阈值超出范围: {profit_threshold}")
                self.set("trading.profit_loss.profit_threshold", 1.0)
            
            loss_threshold = self.get("trading.profit_loss.loss_threshold", -3.0)
            if not (-100 <= loss_threshold <= -0.1):
                self.logger.warning(f"止损阈值超出范围: {loss_threshold}")
                self.set("trading.profit_loss.loss_threshold", -3.0)
            
            self.logger.info("配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        try:
            with self.config_lock:
                keys = key.split('.')
                value = self.config
                
                for k in keys:
                    if isinstance(value, dict) and k in value:
                        value = value[k]
                    else:
                        return default
                
                return value
                
        except Exception as e:
            self.logger.error(f"获取配置失败 {key}: {e}")
            return default
    
    def set(self, key: str, value: Any, save: bool = True) -> bool:
        """设置配置值"""
        try:
            with self.config_lock:
                keys = key.split('.')
                config = self.config
                
                # 导航到父级
                for k in keys[:-1]:
                    if k not in config:
                        config[k] = {}
                    config = config[k]
                
                # 设置值
                config[keys[-1]] = value
                
                # 自动保存
                if save and self.get("system.auto_save_config", True):
                    self.save_config()
                
                return True
                
        except Exception as e:
            self.logger.error(f"设置配置失败 {key}: {e}")
            return False
    
    def get_trading_config(self) -> Dict[str, Any]:
        """获取交易配置"""
        return self.get('trading', {})
    
    def get_detection_config(self) -> Dict[str, Any]:
        """获取检测配置"""
        return self.get('detection', {})
    
    def get_ui_config(self) -> Dict[str, Any]:
        """获取界面配置"""
        return self.get('ui', {})
    
    def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        return self.get('system', {})
    
    def update_trading_parameters(self, params: Dict[str, Any]) -> bool:
        """更新交易参数"""
        try:
            current_params = self.get('trading.parameters', {})
            current_params.update(params)
            return self.set('trading.parameters', current_params)
        except Exception as e:
            self.logger.error(f"更新交易参数失败: {e}")
            return False
    
    def update_profit_loss_config(self, config: Dict[str, Any]) -> bool:
        """更新止盈止损配置"""
        try:
            current_config = self.get('trading.profit_loss', {})
            current_config.update(config)
            return self.set('trading.profit_loss', current_config)
        except Exception as e:
            self.logger.error(f"更新止盈止损配置失败: {e}")
            return False
    
    def reset_to_default(self) -> bool:
        """重置为默认配置"""
        try:
            with self.config_lock:
                self.config = self.get_default_config()
                return self.save_config()
        except Exception as e:
            self.logger.error(f"重置配置失败: {e}")
            return False
    
    def export_config(self, export_file: str) -> bool:
        """导出配置"""
        try:
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            self.logger.info(f"配置导出成功: {export_file}")
            return True
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            return False
    
    def import_config(self, import_file: str) -> bool:
        """导入配置"""
        try:
            with open(import_file, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            with self.config_lock:
                self.config = imported_config
                self.validate_config()
                self.save_config()
            
            self.logger.info(f"配置导入成功: {import_file}")
            return True
        except Exception as e:
            self.logger.error(f"导入配置失败: {e}")
            return False

# 全局配置管理器实例
config_manager = EnhancedConfigManager()
