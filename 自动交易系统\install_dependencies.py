#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本
一步一步安装自动交易系统所需的依赖包
"""

import subprocess
import sys
import os
import time

def print_step(step_num, description):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"步骤 {step_num}: {description}")
    print(f"{'='*60}")

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n正在执行: {description}")
    print(f"命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {description} - 成功")
            if result.stdout:
                print(f"输出: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} - 失败")
            if result.stderr:
                print(f"错误: {result.stderr.strip()}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - 超时")
        return False
    except Exception as e:
        print(f"❌ {description} - 异常: {e}")
        return False

def check_python_version():
    """检查Python版本"""
    print_step(1, "检查Python版本")
    
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 7:
        print("✅ Python版本符合要求")
        return True
    else:
        print("❌ Python版本过低，建议使用Python 3.7+")
        return False

def upgrade_pip():
    """升级pip"""
    print_step(2, "升级pip")
    
    commands = [
        "python -m pip install --upgrade pip",
        "pip --version"
    ]
    
    success = True
    for cmd in commands:
        if not run_command(cmd, f"执行: {cmd}"):
            success = False
    
    return success

def install_core_packages():
    """安装核心包"""
    print_step(3, "安装核心依赖包")
    
    # 核心包列表（按重要性排序）
    packages = [
        ("PyQt5", "GUI界面库"),
        ("opencv-python", "图像处理库"),
        ("numpy", "数值计算库"),
        ("pyautogui", "自动化操作库"),
        ("Pillow", "图像处理库"),
        ("pytesseract", "OCR文字识别库"),
        ("psutil", "系统进程管理库")
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package, description in packages:
        print(f"\n正在安装 {package} ({description})...")
        
        # 尝试安装
        cmd = f"pip install {package}"
        if run_command(cmd, f"安装 {package}"):
            success_count += 1
            
            # 验证安装
            try:
                __import__(package.lower().replace('-', '_'))
                print(f"✅ {package} 安装并验证成功")
            except ImportError:
                print(f"⚠️ {package} 安装成功但导入失败")
        else:
            print(f"❌ {package} 安装失败")
    
    print(f"\n安装结果: {success_count}/{total_count} 个包安装成功")
    return success_count == total_count

def install_optional_packages():
    """安装可选包"""
    print_step(4, "安装可选依赖包")
    
    optional_packages = [
        ("requests", "HTTP请求库"),
        ("matplotlib", "图表绘制库"),
        ("pandas", "数据分析库")
    ]
    
    for package, description in optional_packages:
        print(f"\n正在安装可选包 {package} ({description})...")
        cmd = f"pip install {package}"
        run_command(cmd, f"安装 {package}")

def verify_installation():
    """验证安装结果"""
    print_step(5, "验证安装结果")
    
    test_imports = [
        ("PyQt5", "PyQt5.QtWidgets"),
        ("OpenCV", "cv2"),
        ("NumPy", "numpy"),
        ("PyAutoGUI", "pyautogui"),
        ("Pillow", "PIL"),
        ("Tesseract", "pytesseract"),
        ("PSUtil", "psutil")
    ]
    
    success_count = 0
    total_count = len(test_imports)
    
    print("\n验证导入:")
    for name, module in test_imports:
        try:
            __import__(module)
            print(f"✅ {name} - 可以正常导入")
            success_count += 1
        except ImportError as e:
            print(f"❌ {name} - 导入失败: {e}")
    
    print(f"\n验证结果: {success_count}/{total_count} 个模块可以正常导入")
    
    if success_count == total_count:
        print("\n🎉 所有依赖安装成功！")
        return True
    else:
        print(f"\n⚠️ 还有 {total_count - success_count} 个模块存在问题")
        return False

def create_test_script():
    """创建测试脚本"""
    print_step(6, "创建测试脚本")
    
    test_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
依赖测试脚本
\"\"\"

def test_dependencies():
    \"\"\"测试所有依赖\"\"\"
    print("测试依赖导入...")
    
    tests = [
        ("PyQt5", lambda: __import__("PyQt5.QtWidgets")),
        ("OpenCV", lambda: __import__("cv2")),
        ("NumPy", lambda: __import__("numpy")),
        ("PyAutoGUI", lambda: __import__("pyautogui")),
        ("Pillow", lambda: __import__("PIL")),
        ("Tesseract", lambda: __import__("pytesseract")),
        ("PSUtil", lambda: __import__("psutil"))
    ]
    
    for name, test_func in tests:
        try:
            test_func()
            print(f"✅ {name}")
        except ImportError as e:
            print(f"❌ {name}: {e}")

if __name__ == "__main__":
    test_dependencies()
"""
    
    try:
        with open("test_dependencies.py", "w", encoding="utf-8") as f:
            f.write(test_script)
        print("✅ 测试脚本创建成功: test_dependencies.py")
        return True
    except Exception as e:
        print(f"❌ 测试脚本创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 自动交易系统依赖安装程序")
    print("这个脚本将帮助您安装所有必需的依赖包")
    
    # 检查Python版本
    if not check_python_version():
        print("\n❌ Python版本不符合要求，请升级Python后重试")
        return False
    
    # 升级pip
    if not upgrade_pip():
        print("\n⚠️ pip升级失败，但继续安装...")
    
    # 安装核心包
    if not install_core_packages():
        print("\n⚠️ 部分核心包安装失败，但继续...")
    
    # 安装可选包
    install_optional_packages()
    
    # 验证安装
    success = verify_installation()
    
    # 创建测试脚本
    create_test_script()
    
    print(f"\n{'='*60}")
    if success:
        print("🎉 依赖安装完成！")
        print("\n下一步:")
        print("1. 运行 'python test_dependencies.py' 测试依赖")
        print("2. 运行 'python smart_trading.py' 启动交易系统")
    else:
        print("⚠️ 依赖安装部分完成")
        print("\n建议:")
        print("1. 检查网络连接")
        print("2. 尝试使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/")
        print("3. 手动安装失败的包")
    
    print(f"{'='*60}")
    return success

if __name__ == "__main__":
    main()
