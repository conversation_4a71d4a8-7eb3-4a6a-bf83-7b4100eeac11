#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多线程GUI测试脚本
测试GUI是否能正常启动而不卡死
"""

import sys
import os
import time
from pathlib import Path

# 添加src目录到路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_threaded_gui():
    """测试多线程GUI启动"""
    print("🧪 开始多线程GUI启动测试...")
    
    try:
        # 导入PyQt5
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        print("✅ PyQt5导入成功")
        
        # 创建应用
        app = QApplication(sys.argv)
        print("✅ Qt应用创建成功")
        
        # 导入GUI模块
        from trading.trading_gui import SmartTradingWindow
        print("✅ GUI模块导入成功")
        
        # 创建主窗口
        print("🔧 正在创建主窗口...")
        window = SmartTradingWindow()
        print("✅ 主窗口创建成功")
        
        # 显示窗口
        window.show()
        print("✅ 窗口显示成功")
        
        # 设置自动退出定时器（15秒后自动退出）
        def check_status():
            print("🔍 检查初始化状态...")
            if window.trading_engine:
                print("✅ 交易引擎已初始化")
                
                # 检查检测器状态
                if hasattr(window.trading_engine, 'jingtao_detector') and window.trading_engine.jingtao_detector:
                    print("✅ 景陶易购专用检测器已初始化")
                else:
                    print("⚠️ 景陶易购专用检测器未初始化")
                
                if hasattr(window.trading_engine, 'auto_confirm_handler') and window.trading_engine.auto_confirm_handler:
                    print("✅ 自动确认处理器已初始化")
                else:
                    print("⚠️ 自动确认处理器未初始化")
                
                if hasattr(window.trading_engine, 'real_price_detector') and window.trading_engine.real_price_detector:
                    print("✅ 真实价格检测器已初始化")
                else:
                    print("⚠️ 真实价格检测器未初始化")
                
                print("🎉 多线程GUI测试成功！")
            else:
                print("⚠️ 交易引擎仍在初始化中...")
            
            # 5秒后退出
            QTimer.singleShot(5000, app.quit)
        
        # 10秒后检查状态
        QTimer.singleShot(10000, check_status)
        
        print("🚀 GUI已启动，将在15秒后自动退出...")
        print("   观察是否出现卡死现象...")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 多线程GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_threaded_gui()
