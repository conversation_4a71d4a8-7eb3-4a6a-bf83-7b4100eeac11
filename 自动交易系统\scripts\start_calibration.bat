@echo off
chcp 65001
title 景陶易购坐标校准工具
echo.
echo 🎯 景陶易购智能坐标校准工具
echo =====================================
echo.
echo 📋 请选择校准工具:
echo.
echo [1] 智能校准工具 (推荐) - 功能最全面
echo [2] 快速校准工具 - 简化版本  
echo [3] GUI校准工具 - 图形界面
echo [4] 查看使用说明
echo [5] 测试现有配置
echo [0] 退出
echo.
set /p choice=请输入选择 (0-5): 

if "%choice%"=="1" goto smart
if "%choice%"=="2" goto quick  
if "%choice%"=="3" goto gui
if "%choice%"=="4" goto guide
if "%choice%"=="5" goto test
if "%choice%"=="0" goto exit
goto invalid

:smart
echo.
echo 🚀 启动智能校准工具...
python smart_calibrator.py
goto end

:quick
echo.
echo 🚀 启动快速校准工具...
python quick_calibrator.py
goto end

:gui
echo.
echo 🚀 启动GUI校准工具...
python auto_coordinate_calibrator.py
goto end

:guide
echo.
echo 📖 打开使用说明...
if exist "CALIBRATION_GUIDE.md" (
    start notepad "CALIBRATION_GUIDE.md"
) else (
    echo ❌ 使用说明文件不存在
)
pause
goto start

:test
echo.
echo 🧪 测试现有配置...
python main_stable.py console
goto end

:invalid
echo.
echo ❌ 无效选择，请重新输入
pause
goto start

:start
cls
goto :EOF

:end
echo.
echo ✅ 操作完成
pause

:exit
echo.
echo 👋 再见！
pause