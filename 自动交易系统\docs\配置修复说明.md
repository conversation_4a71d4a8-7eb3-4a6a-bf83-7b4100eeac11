# 配置修复说明

## 问题描述

在启动交易系统时出现以下错误：
```
⚠️ 真实价格检测器初始化失败: 'SmartTradingEngine' object has no attribute 'config'
```

## 问题原因

`SmartTradingEngine`类在初始化真实价格检测器时尝试访问`self.config`，但该属性在`__init__`方法中没有正确初始化。

## 修复方案

### 1. 修复SmartTradingEngine初始化

在`src/trading/trading_engine.py`中：

```python
def __init__(self, client_path: str = None, config=None):
    self.logger = logging.getLogger(__name__)
    
    # 初始化配置管理器
    if config is not None:
        self.config = config
    else:
        # 创建默认配置对象
        try:
            from config.config_manager import EnhancedConfigManager
            self.config = EnhancedConfigManager()
        except ImportError:
            # 创建简单配置对象作为备用
            class SimpleConfig:
                def get(self, key, default=None):
                    return default
            self.config = SimpleConfig()
```

### 2. 修复GUI中的引擎初始化

在`src/trading/trading_gui.py`中：

```python
def init_trading_engine(self):
    try:
        from config.config_manager import EnhancedConfigManager
        config = EnhancedConfigManager()
        self.trading_engine = SmartTradingEngine(config=config)
    except ImportError:
        self.trading_engine = SmartTradingEngine()
```

### 3. 新增手动价格选择启动脚本

创建了`main_stable_manual.py`，提供：
- 启动时选择价格检测模式
- 自动设置价格区域（如果需要）
- 更好的错误处理

## 使用方法

### 方法1：使用新的启动脚本（推荐）

```bash
python main_stable_manual.py
```

或双击：
```
scripts/start_manual_trading.bat
```

### 方法2：手动配置

1. 先设置价格区域：
```bash
python tools/manual_price_setup.py
```

2. 修改配置文件`config/trading_config.json`：
```json
{
  "price_detection": {
    "use_manual_price_selection": true
  }
}
```

3. 启动系统：
```bash
python main_stable.py
```

## 预期效果

修复后，系统应该能够：
- ✅ 正常初始化配置管理器
- ✅ 成功启动真实价格检测器
- ✅ 支持手动价格区域选择模式
- ✅ 在配置缺失时提供合理的备用方案

## 测试验证

启动系统后，日志应显示：
```
✅ 真实价格检测器初始化成功 - 手动区域选择模式
```

而不是：
```
⚠️ 真实价格检测器初始化失败: 'SmartTradingEngine' object has no attribute 'config'
```