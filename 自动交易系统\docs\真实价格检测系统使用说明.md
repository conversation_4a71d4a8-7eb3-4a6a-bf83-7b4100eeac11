# 🎯 景陶易购真实价格检测系统使用说明

## 📋 概述

全新的真实价格检测系统专门为景陶易购交易界面设计，能够：
- 🔍 实时从界面中提取真实价格数据
- 💰 自动输入价格到交易界面
- 📊 支持多区域价格监控
- 🛡️ 提供多层次的价格获取降级方案
- ⚡ 无需手动输入，完全自动化

## 🚀 快速开始

### 1. 基本使用

```python
from trading.real_price_detector import RealPriceDetector

# 创建检测器
detector = RealPriceDetector()

# 获取当前价格
price = detector.get_price_with_fallback()
print(f"当前价格: {price}")

# 自动输入价格
detector.auto_input_price(price + 1)  # 输入当前价格+1
```

### 2. 集成到交易引擎

```python
from trading.trading_engine import SmartTradingEngine

# 创建交易引擎（自动集成价格检测器）
engine = SmartTradingEngine()

# 获取真实价格
screenshot = engine.real_price_detector.get_screenshot()
real_price = engine.get_current_price_from_display(screenshot)

# 自动填入价格
engine.auto_fill_price(price_adjustment=1)  # 当前价格+1
```

## 🔧 功能详解

### 价格区域配置

系统预配置了以下价格检测区域：

| 区域名称 | 描述 | 位置 |
|---------|------|------|
| `main_price` | 主要价格显示区域 | 左上角WFLM后 |
| `current_price` | 当前最新价格 | 最新价格区域 |
| `bid_price` | 买入价格 | 买价区域 |
| `ask_price` | 卖出价格 | 卖价区域 |
| `input_price` | 价格输入框 | 交易下单区域 |

### 主要方法

#### 1. `get_real_price(region_name)`
从指定区域获取价格

```python
price = detector.get_real_price('current_price')
```

#### 2. `get_multiple_prices()`
获取多个区域的价格进行对比

```python
prices = detector.get_multiple_prices()
for region, price in prices.items():
    print(f"{region}: {price}")
```

#### 3. `get_price_with_fallback()`
智能价格获取（推荐使用）

```python
# 自动按优先级获取价格，带降级方案
price = detector.get_price_with_fallback()
```

优先级顺序：
1. 当前价格区域 OCR识别
2. 主价格区域 OCR识别  
3. 买价区域 OCR识别
4. 历史价格 + 随机波动
5. 默认价格范围随机值

#### 4. `auto_input_price(target_price, price_adjustment)`
自动输入价格到交易界面

```python
# 输入指定价格
detector.auto_input_price(1405)

# 输入当前价格+调整值
detector.auto_input_price(price_adjustment=1)  # +1元
detector.auto_input_price(price_adjustment=-1) # -1元
```

## 📊 使用场景

### 场景1：实时价格监控

```python
import time

detector = RealPriceDetector()

while True:
    current_price = detector.get_price_with_fallback()
    print(f"实时价格: {current_price}")
    time.sleep(5)  # 每5秒检测一次
```

### 场景2：自动下单

```python
detector = RealPriceDetector()

# 获取当前价格
current_price = detector.get_price_with_fallback()

# 买入：当前价格-1
buy_price = current_price - 1
detector.auto_input_price(buy_price)
# 然后执行买入操作...

# 卖出：当前价格+1  
sell_price = current_price + 1
detector.auto_input_price(sell_price)
# 然后执行卖出操作...
```

### 场景3：价格策略

```python
detector = RealPriceDetector()

# 获取多区域价格
prices = detector.get_multiple_prices()
main_price = prices.get('main_price')
bid_price = prices.get('bid_price')
ask_price = prices.get('ask_price')

# 基于价差的策略
if ask_price and bid_price:
    spread = ask_price - bid_price
    if spread > 2:  # 价差大于2元
        # 执行套利策略
        detector.auto_input_price(bid_price + 1)
```

## 🛠️ 测试和调试

### 运行测试脚本

```bash
# 基本功能测试
python test_real_price.py

# 完整演示
python demo_real_price.py
```

### 价格区域校准

如果价格检测不准确，可以重新校准：

```python
detector = RealPriceDetector()
detector.calibrate_price_regions()  # 交互式校准
```

### 调试信息

系统会自动保存调试截图到 `logs/` 目录，文件名包含时间戳和区域名称。

## ⚠️ 注意事项

### 1. 系统要求
- Python 3.9+
- 景陶易购客户端正常运行
- 推荐安装 pytesseract（提升OCR准确率）

### 2. 使用建议
- 确保交易界面清晰可见，无遮挡
- 建议在稳定网络环境下使用
- 价格输入前请确认交易界面已激活

### 3. 错误处理
- 系统内置多层降级方案，确保始终能获取到合理价格
- 如果OCR失败，会使用历史价格+随机波动
- 所有操作都有详细的日志记录

## 🔄 与现有系统集成

### 交易引擎集成

新的价格检测器已自动集成到 `SmartTradingEngine` 中：

```python
engine = SmartTradingEngine()

# 引擎会优先使用真实价格检测器
price = engine.get_current_price_from_display(screenshot)

# 自动填入价格也会使用新系统
engine.auto_fill_price(price_adjustment=1)
```

### 向后兼容

系统完全向后兼容，如果新的价格检测器不可用，会自动回退到原有的方法。

## 📞 技术支持

如遇问题，请检查：
1. 📋 日志文件：`logs/trading_log_*.txt`
2. 📸 调试截图：`logs/debug_price_region_*.png`
3. 📊 系统状态：调用 `detector.get_stats()`

---

**🎉 现在您可以享受完全自动化的真实价格检测和输入功能！**