#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测器测试脚本
测试各种检测器是否能正确初始化
"""

import sys
import os
from pathlib import Path

# 添加src目录到路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_color_detector():
    """测试颜色检测器"""
    print("🎨 测试景陶易购专用颜色检测器...")
    
    try:
        from ui.color_detection_fix import JingTaoColorDetector
        detector = JingTaoColorDetector()
        print("✅ 景陶易购专用颜色检测器初始化成功")
        return True
    except Exception as e:
        print(f"❌ 景陶易购专用颜色检测器初始化失败: {e}")
        return False

def test_auto_confirm_handler():
    """测试自动确认处理器"""
    print("🔧 测试自动确认处理器...")
    
    try:
        from ui.auto_confirm_handler import AutoConfirmHandler
        handler = AutoConfirmHandler()
        print("✅ 自动确认处理器初始化成功")
        return True
    except Exception as e:
        print(f"❌ 自动确认处理器初始化失败: {e}")
        return False

def test_real_price_detector():
    """测试真实价格检测器"""
    print("💰 测试真实价格检测器...")
    
    try:
        from trading.real_price_detector import RealPriceDetector
        detector = RealPriceDetector(use_manual_selection=False)
        print("✅ 真实价格检测器初始化成功")
        return True
    except Exception as e:
        print(f"❌ 真实价格检测器初始化失败: {e}")
        return False

def test_enhanced_detector():
    """测试增强检测器"""
    print("🔍 测试增强检测器...")
    
    try:
        from core.enhanced_detection import enhanced_detector
        print("✅ 增强检测器导入成功")
        return True
    except Exception as e:
        print(f"❌ 增强检测器导入失败: {e}")
        return False

def test_trading_engine_integration():
    """测试交易引擎集成"""
    print("🚀 测试交易引擎集成...")
    
    try:
        from trading.trading_engine import SmartTradingEngine
        
        # 创建简单配置
        class SimpleConfig:
            def get(self, key, default=None):
                config_map = {
                    'price_detection.use_manual_price_selection': False,
                    'trading.angle_threshold': 20,
                    'trading.default_quantity': 1,
                    'trading.check_interval': 5
                }
                return config_map.get(key, default)
        
        config = SimpleConfig()
        engine = SmartTradingEngine(config=config)
        print("✅ 交易引擎基础初始化成功")
        
        # 测试延迟初始化
        engine.delayed_init_components()
        print("✅ 交易引擎延迟组件初始化成功")
        
        # 检查各个检测器状态
        print("\n🔧 检测器状态检查:")
        
        if hasattr(engine, 'jingtao_detector') and engine.jingtao_detector:
            print("   ✅ 景陶易购专用检测器")
        else:
            print("   ❌ 景陶易购专用检测器不可用")
        
        if hasattr(engine, 'auto_confirm_handler') and engine.auto_confirm_handler:
            print("   ✅ 自动确认处理器")
        else:
            print("   ❌ 自动确认处理器不可用")
        
        if hasattr(engine, 'real_price_detector') and engine.real_price_detector:
            print("   ✅ 真实价格检测器")
        else:
            print("   ❌ 真实价格检测器不可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 交易引擎集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 开始检测器测试...")
    print("=" * 50)
    
    results = []
    
    # 测试各个检测器
    results.append(("颜色检测器", test_color_detector()))
    results.append(("自动确认处理器", test_auto_confirm_handler()))
    results.append(("真实价格检测器", test_real_price_detector()))
    results.append(("增强检测器", test_enhanced_detector()))
    results.append(("交易引擎集成", test_trading_engine_integration()))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    success_count = 0
    for name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 所有检测器测试通过！")
    else:
        print("⚠️ 部分检测器测试失败，请检查相关配置")

if __name__ == "__main__":
    main()
