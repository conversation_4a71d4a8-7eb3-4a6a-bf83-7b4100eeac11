#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实价格检测器测试脚本
"""

import sys
import logging
from pathlib import Path

# 添加src路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

try:
    from trading.real_price_detector import RealPriceDetector
    print("✅ 真实价格检测器导入成功")
except ImportError as e:
    print(f"❌ 真实价格检测器导入失败: {e}")
    sys.exit(1)

def main():
    """主测试函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🔍 开始测试真实价格检测器...")
    
    # 创建价格检测器
    detector = RealPriceDetector()
    
    print("\n1. 测试获取统计信息:")
    stats = detector.get_stats()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    print("\n2. 测试获取单个区域价格:")
    regions_to_test = ['current_price', 'main_price', 'bid_price', 'ask_price']
    
    for region_name in regions_to_test:
        print(f"   测试区域: {region_name}")
        try:
            price = detector.get_real_price(region_name)
            if price:
                print(f"   ✅ 获取到价格: {price}")
            else:
                print(f"   ⚠️ 未能获取价格")
        except Exception as e:
            print(f"   ❌ 获取价格失败: {e}")
    
    print("\n3. 测试获取多区域价格:")
    try:
        prices = detector.get_multiple_prices()
        for region, price in prices.items():
            if price:
                print(f"   {region}: {price}")
            else:
                print(f"   {region}: 未检测到")
    except Exception as e:
        print(f"   ❌ 获取多区域价格失败: {e}")
    
    print("\n4. 测试价格降级获取:")
    try:
        fallback_price = detector.get_price_with_fallback()
        print(f"   降级价格: {fallback_price}")
    except Exception as e:
        print(f"   ❌ 降级价格获取失败: {e}")
    
    print("\n5. 测试自动输入价格:")
    test_price = 1405.0
    try:
        # 注意：这会实际执行点击和输入操作，请确保交易界面已打开
        confirm = input(f"   是否测试自动输入价格 {test_price}？(y/N): ")
        if confirm.lower() == 'y':
            success = detector.auto_input_price(test_price)
            if success:
                print(f"   ✅ 自动输入价格成功: {test_price}")
            else:
                print(f"   ❌ 自动输入价格失败")
        else:
            print("   跳过自动输入测试")
    except Exception as e:
        print(f"   ❌ 自动输入价格测试失败: {e}")
    
    print("\n🎯 测试完成！")
    
    # 显示最终统计
    final_stats = detector.get_stats()
    print("\n📊 最终统计:")
    for key, value in final_stats.items():
        print(f"   {key}: {value}")

if __name__ == "__main__":
    main()