<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="HtmlUnknownTag" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myValues">
        <value>
          <list size="7">
            <item index="0" class="java.lang.String" itemvalue="nobr" />
            <item index="1" class="java.lang.String" itemvalue="noembed" />
            <item index="2" class="java.lang.String" itemvalue="comment" />
            <item index="3" class="java.lang.String" itemvalue="noscript" />
            <item index="4" class="java.lang.String" itemvalue="embed" />
            <item index="5" class="java.lang.String" itemvalue="script" />
            <item index="6" class="java.lang.String" itemvalue="div" />
          </list>
        </value>
      </option>
      <option name="myCustomValuesEnabled" value="true" />
    </inspection_tool>
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="7">
            <item index="0" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="1" class="java.lang.String" itemvalue="mutagen" />
            <item index="2" class="java.lang.String" itemvalue="flask-cors" />
            <item index="3" class="java.lang.String" itemvalue="lxml" />
            <item index="4" class="java.lang.String" itemvalue="requests" />
            <item index="5" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="6" class="java.lang.String" itemvalue="flask" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>