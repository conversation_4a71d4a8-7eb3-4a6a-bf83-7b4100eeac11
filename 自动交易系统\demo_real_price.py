#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实价格检测演示脚本
展示如何在交易系统中使用真实价格检测器
"""

import sys
import time
import logging
from pathlib import Path

# 添加src路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/real_price_demo.log'),
            logging.StreamHandler()
        ]
    )

def demo_price_detection():
    """演示价格检测功能"""
    print("=" * 60)
    print("🎯 景陶易购真实价格检测系统演示")
    print("=" * 60)
    
    try:
        from trading.real_price_detector import RealPriceDetector
        print("✅ 真实价格检测器导入成功")
    except ImportError as e:
        print(f"❌ 无法导入真实价格检测器: {e}")
        return
    
    # 创建检测器
    detector = RealPriceDetector()
    
    print("\n📊 系统状态:")
    stats = detector.get_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print("\n🔍 开始实时价格监控...")
    print("按 Ctrl+C 停止监控")
    
    try:
        while True:
            print(f"\n⏰ {time.strftime('%H:%M:%S')}")
            
            # 获取真实价格
            current_price = detector.get_price_with_fallback()
            print(f"💰 当前价格: {current_price:.2f}")
            
            # 获取多区域价格对比
            prices = detector.get_multiple_prices()
            if prices:
                print("📈 多区域价格对比:")
                for region, price in prices.items():
                    if price:
                        print(f"  {region}: {price:.2f}")
                    else:
                        print(f"  {region}: 未检测到")
            
            # 模拟价格调整演示
            print(f"💡 演示价格调整:")
            print(f"  买入价格 (当前-1): {current_price-1:.0f}")
            print(f"  卖出价格 (当前+1): {current_price+1:.0f}")
            
            time.sleep(5)  # 每5秒检测一次
            
    except KeyboardInterrupt:
        print("\n⏹️ 监控已停止")

def demo_auto_input():
    """演示自动输入功能"""
    print("\n🎯 自动输入价格演示")
    print("确保景陶易购交易界面已打开且可见")
    
    try:
        from trading.real_price_detector import RealPriceDetector
        detector = RealPriceDetector()
        
        # 获取当前价格
        current_price = detector.get_price_with_fallback()
        print(f"💰 当前检测价格: {current_price:.2f}")
        
        # 询问用户是否要测试自动输入
        response = input("是否要测试自动输入价格功能？(y/N): ")
        if response.lower() != 'y':
            print("跳过自动输入测试")
            return
        
        # 测试不同的价格调整
        test_cases = [
            (0, "当前价格"),
            (-1, "买入价格 (当前-1)"),
            (1, "卖出价格 (当前+1)")
        ]
        
        for adjustment, description in test_cases:
            print(f"\n🔧 测试: {description}")
            target_price = current_price + adjustment
            
            confirm = input(f"输入价格 {target_price:.0f}？(y/N): ")
            if confirm.lower() == 'y':
                success = detector.auto_input_price(target_price)
                if success:
                    print(f"✅ 成功输入价格: {target_price:.0f}")
                else:
                    print("❌ 输入价格失败")
                time.sleep(2)  # 等待一下再进行下一个测试
            else:
                print("跳过此测试")
                
    except Exception as e:
        print(f"❌ 自动输入演示失败: {e}")

def demo_integration():
    """演示与交易引擎的集成"""
    print("\n🎯 交易引擎集成演示")
    
    try:
        from trading.trading_engine import SmartTradingEngine
        print("✅ 交易引擎导入成功")
        
        # 创建交易引擎
        engine = SmartTradingEngine()
        
        # 检查真实价格检测器是否正常工作
        if hasattr(engine, 'real_price_detector') and engine.real_price_detector:
            print("✅ 交易引擎中的真实价格检测器可用")
            
            # 测试获取价格
            print("\n📊 通过交易引擎获取价格:")
            screenshot = engine.real_price_detector.get_screenshot()
            if screenshot is not None:
                price = engine.get_current_price_from_display(screenshot)
                print(f"💰 交易引擎获取的价格: {price}")
            else:
                print("❌ 无法获取截图")
        else:
            print("❌ 交易引擎中的真实价格检测器不可用")
            
    except Exception as e:
        print(f"❌ 交易引擎集成测试失败: {e}")

def main():
    """主函数"""
    setup_logging()
    
    print("选择演示模式:")
    print("1. 价格检测演示")
    print("2. 自动输入演示") 
    print("3. 交易引擎集成演示")
    print("4. 全部演示")
    
    choice = input("请选择 (1-4): ").strip()
    
    if choice == '1':
        demo_price_detection()
    elif choice == '2':
        demo_auto_input()
    elif choice == '3':
        demo_integration()
    elif choice == '4':
        demo_integration()
        input("\n按回车继续自动输入演示...")
        demo_auto_input()
        input("\n按回车继续价格检测演示...")
        demo_price_detection()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()