# 手动价格区域选择使用说明

## 概述

由于OCR识别价格存在准确性问题，系统现在支持**手动价格区域选择**模式。用户可以通过在客户端窗口上手动选择价格监测区域，系统将使用这些区域进行价格监测，而不依赖OCR识别。

## 功能特点

✅ **完全替代OCR** - 不再依赖文字识别技术  
✅ **用户友好** - 图形界面选择价格区域  
✅ **准确度高** - 直接使用用户指定的区域  
✅ **配置保存** - 一次设置，持久使用  
✅ **实时监控** - 支持多区域同时监测  

## 使用步骤

### 1. 启动价格区域设置工具

**方法一：使用批处理文件**
```
双击运行：scripts/setup_price_regions.bat
```

**方法二：使用Python脚本**
```bash
cd 自动交易系统
python tools/manual_price_setup.py
```

### 2. 设置价格区域

1. **确保景陶易购客户端已打开**
   - 客户端必须显示完整的价格信息
   - 价格区域应该清晰可见

2. **点击"设置价格区域"按钮**
   - 系统会依次提示设置各个价格区域
   - 每次会显示屏幕截图供选择

3. **选择价格区域**
   - 在截图上**拖拽鼠标**选择价格显示区域
   - 确保选择区域完整包含价格数字
   - 按**ESC键**可以取消当前选择

4. **需要设置的区域**
   - `main_price`: 主要价格显示区域
   - `current_price`: 当前最新价格
   - `bid_price`: 买入价格
   - `ask_price`: 卖出价格

### 3. 测试设置

1. **点击"测试设置"按钮**
   - 系统会尝试从各个区域获取价格
   - 显示测试结果

2. **查看测试结果**
   - ✓ 表示该区域价格获取成功
   - ✗ 表示该区域价格获取失败

### 4. 启用手动价格选择模式

**方法一：修改配置文件**
```json
{
  "price_detection": {
    "use_manual_price_selection": true
  }
}
```

**方法二：使用专用配置文件**
```bash
# 复制手动价格选择配置文件
cp config/trading_config_manual.json config/trading_config.json
```

## 演示和测试

### 运行演示程序
```bash
python demo_manual_price.py
```

演示程序提供两种模式：
1. **OCR模式** - 传统的文字识别方式
2. **手动区域选择模式** - 新的手动选择方式

### 在交易系统中使用

1. **确保已设置价格区域**
2. **启用手动价格选择模式**（修改配置文件）
3. **正常启动交易系统**

```bash
python main_stable.py
```

系统会自动使用手动选择的价格区域。

## 配置选项

### 价格检测配置
```json
{
  "price_detection": {
    "use_manual_price_selection": true,      // 启用手动价格选择
    "ocr_fallback_enabled": true,            // 启用OCR备用方案
    "price_update_interval": 1.0,            // 价格更新间隔（秒）
    "price_validation_range": {
      "min_price": 500,                      // 最小有效价格
      "max_price": 2000                      // 最大有效价格
    }
  }
}
```

## 故障排除

### 1. 价格区域选择失败
- **原因**：屏幕截图失败或鼠标操作问题
- **解决**：重新运行设置工具，确保客户端正常显示

### 2. 价格获取失败
- **原因**：选择的区域不包含价格或区域太小
- **解决**：重新选择区域，确保完整包含价格数字

### 3. 系统无法识别手动区域
- **原因**：配置文件损坏或路径问题
- **解决**：删除`config/price_regions_manual.json`，重新设置

### 4. 导入错误
- **原因**：依赖项缺失
- **解决**：
```bash
pip install opencv-python pyautogui tkinter numpy
```

## 文件说明

### 核心文件
- `tools/price_region_selector.py` - 价格区域选择器核心逻辑
- `tools/manual_price_setup.py` - 图形界面设置工具
- `src/trading/real_price_detector.py` - 集成了手动选择功能的价格检测器

### 配置文件
- `config/price_regions_manual.json` - 手动选择的价格区域配置
- `config/trading_config_manual.json` - 启用手动价格选择的交易配置

### 脚本文件
- `scripts/setup_price_regions.bat` - Windows批处理启动脚本
- `demo_manual_price.py` - 演示程序

## 技术原理

手动价格区域选择使用以下技术：

1. **屏幕截图** - 使用`pyautogui`获取实时屏幕图像
2. **区域提取** - 根据用户选择的坐标提取价格区域
3. **像素分析** - 分析区域内的颜色和亮度变化
4. **价格估算** - 基于像素特征估算价格值

虽然不是完美的数字识别，但比OCR更稳定可靠。

## 注意事项

⚠️ **重要提醒**：
- 手动价格选择**不会进行精确的数字识别**
- 系统使用**像素分析和估算**来获取价格
- 价格值可能**不是100%准确**，但趋势变化是可靠的
- 建议在**测试模式**下验证价格获取效果

✅ **优势**：
- 不依赖OCR，更稳定
- 用户可控，选择精准
- 配置一次，长期使用
- 支持多价格区域同时监测

---

*如有问题，请查看日志文件或联系技术支持。*