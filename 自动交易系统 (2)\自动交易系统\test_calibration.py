#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试坐标校准功能
"""

import tkinter as tk
from tkinter import messagebox
import pyautogui
import json
import os

def test_calibration():
    """测试校准功能"""
    print("🔧 测试坐标校准功能")
    
    # 检查配置文件
    config_file = 'smart_coordinates_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"✅ 找到配置文件: {config_file}")
        
        if 'button_positions' in config:
            buttons = config['button_positions']
            print(f"📍 已校准按钮数量: {len(buttons)}")
            
            for button_key, button_data in buttons.items():
                name = button_data.get('name', button_key)
                x = button_data.get('x', 0)
                y = button_data.get('y', 0)
                print(f"  - {name}: ({x:.4f}, {y:.4f})")
                
                if 'calibrated_absolute' in button_data:
                    abs_pos = button_data['calibrated_absolute']
                    print(f"    绝对坐标: ({abs_pos['x']}, {abs_pos['y']})")
        else:
            print("❌ 配置文件中没有按钮位置信息")
    else:
        print("❌ 配置文件不存在")
    
    # 测试屏幕分辨率
    screen_width, screen_height = pyautogui.size()
    print(f"📏 屏幕分辨率: {screen_width} x {screen_height}")

def test_mouse_position():
    """测试鼠标位置获取"""
    print("\n🖱️ 测试鼠标位置获取")
    try:
        x, y = pyautogui.position()
        print(f"当前鼠标位置: ({x}, {y})")
        
        # 转换为相对坐标
        screen_width, screen_height = pyautogui.size()
        rel_x = x / screen_width
        rel_y = y / screen_height
        print(f"相对坐标: ({rel_x:.4f}, {rel_y:.4f})")
        
        return True
    except Exception as e:
        print(f"❌ 获取鼠标位置失败: {e}")
        return False

def simulate_calibration_flow():
    """模拟校准流程"""
    print("\n🎯 模拟校准流程")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("校准测试")
    root.geometry("400x300")
    
    # 说明文本
    label = tk.Label(root, text="校准功能测试\n\n"
                               "这个窗口模拟了主程序中的校准界面\n"
                               "实际使用时会有：\n"
                               "- 倒计时提醒\n"
                               "- 实时鼠标位置显示\n"
                               "- 记录坐标功能\n"
                               "- 测试校准位置功能",
                     justify=tk.LEFT, pady=20)
    label.pack()
    
    def test_position():
        try:
            x, y = pyautogui.position()
            messagebox.showinfo("鼠标位置", f"当前鼠标位置: ({x}, {y})")
        except Exception as e:
            messagebox.showerror("错误", f"获取位置失败: {e}")
    
    def close_test():
        root.destroy()
    
    # 按钮
    button_frame = tk.Frame(root)
    button_frame.pack(pady=20)
    
    tk.Button(button_frame, text="获取鼠标位置", command=test_position).pack(side=tk.LEFT, padx=10)
    tk.Button(button_frame, text="关闭", command=close_test).pack(side=tk.LEFT, padx=10)
    
    print("显示测试窗口...")
    root.mainloop()

def main():
    print("=" * 50)
    print("智能交易系统 - 坐标校准功能测试")
    print("=" * 50)
    
    # 测试1: 配置文件检查
    test_calibration()
    
    # 测试2: 鼠标位置
    if test_mouse_position():
        print("✅ 鼠标位置获取正常")
    else:
        print("❌ 鼠标位置获取异常")
    
    # 测试3: 模拟校准界面
    try:
        simulate_calibration_flow()
        print("✅ 校准界面测试完成")
    except Exception as e:
        print(f"❌ 校准界面测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()