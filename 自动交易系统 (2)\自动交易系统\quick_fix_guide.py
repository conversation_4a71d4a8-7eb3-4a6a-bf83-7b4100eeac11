#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复指导工具
帮助用户快速解决坐标校准和交易问题
"""

import json
import os
import pyautogui
import time

def check_coordinates_config():
    """检查坐标配置文件"""
    print("🔍 检查坐标配置...")
    
    config_file = "smart_coordinates_config.json"
    if not os.path.exists(config_file):
        print("❌ 坐标配置文件不存在!")
        print("👉 请运行: python confirm_button_calibrator.py")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        buttons = config.get('button_positions', {})
        print(f"✅ 找到 {len(buttons)} 个按钮配置:")
        
        required_buttons = [
            'buy_mode_button',
            'sell_mode_button', 
            'buy_order_button',
            'sell_order_button',
            'confirm_button'
        ]
        
        missing_buttons = []
        for button in required_buttons:
            if button in buttons:
                print(f"  ✅ {buttons[button]['name']}: ({buttons[button]['x']:.3f}, {buttons[button]['y']:.3f})")
            else:
                missing_buttons.append(button)
                print(f"  ❌ 缺失: {button}")
        
        if missing_buttons:
            print(f"\n⚠️ 缺失 {len(missing_buttons)} 个必要按钮配置!")
            print("👉 请运行校准工具重新配置")
            return False
        else:
            print("\n✅ 所有必要按钮都已配置!")
            return True
            
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def test_coordinates():
    """测试坐标精确度"""
    print("\n🧪 测试坐标精确度...")
    print("⚠️ 请确保交易界面已打开!")
    print("按回车键开始测试...")
    input()
    
    try:
        config_file = "smart_coordinates_config.json"
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        buttons = config.get('button_positions', {})
        screen_width, screen_height = pyautogui.size()
        
        test_buttons = ['buy_mode_button', 'sell_mode_button', 'confirm_button']
        
        for button_key in test_buttons:
            if button_key in buttons:
                button = buttons[button_key]
                x = int(screen_width * button['x'])
                y = int(screen_height * button['y'])
                
                print(f"📍 测试 {button['name']}: ({x}, {y})")
                
                # 移动鼠标到位置（不点击）
                pyautogui.moveTo(x, y)
                print(f"  鼠标已移动到该位置，请检查是否准确")
                print("  如果位置正确请按回车，否则输入'n':")
                
                response = input().strip().lower()
                if response == 'n':
                    print(f"  ❌ {button['name']} 位置不准确!")
                else:
                    print(f"  ✅ {button['name']} 位置正确!")
                
                time.sleep(1)
        
        print("\n🎯 坐标测试完成!")
        
    except Exception as e:
        print(f"❌ 坐标测试失败: {e}")

def show_calibration_guide():
    """显示校准指导"""
    print("\n📖 校准指导:")
    print("="*50)
    print("1. 运行校准工具:")
    print("   python confirm_button_calibrator.py")
    print()
    print("2. 按照提示校准以下按钮:")
    print("   📌 买入模式按钮 (左侧红色买入按钮)")
    print("   📌 卖出模式按钮 (左侧绿色卖出按钮)")  
    print("   📌 买入订立按钮 (下方确认交易按钮)")
    print("   📌 卖出订立按钮 (下方确认交易按钮)")
    print("   📌 确认按钮 (弹出对话框中的确定按钮)")
    print()
    print("3. 校准步骤:")
    print("   - 拍照截屏")
    print("   - 手动点击要校准的按钮位置")
    print("   - 测试点击位置")
    print("   - 保存配置")
    print()
    print("4. 测试交易延迟:")
    print("   - 确认按钮通常在下单后2-3秒出现")
    print("   - 已调整等待时间为3秒")
    print()
    print("⚠️ 注意事项:")
    print("  - 确保界面完全显示且无遮挡")
    print("  - 校准时界面分辨率应与实际使用时一致")
    print("  - 如果检测不准确，可能需要多次校准")

def main():
    """主函数"""
    print("🔧 智能交易系统 - 快速修复指导工具")
    print("="*50)
    
    # 检查配置
    config_ok = check_coordinates_config()
    
    if not config_ok:
        show_calibration_guide()
        return
    
    # 提供测试选项
    print("\n🎯 配置检查通过! 选择操作:")
    print("1. 测试坐标精确度")
    print("2. 查看校准指导") 
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == '1':
        test_coordinates()
    elif choice == '2':
        show_calibration_guide()
    else:
        print("退出工具")

if __name__ == "__main__":
    main()