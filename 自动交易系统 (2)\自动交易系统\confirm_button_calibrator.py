#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
确认按钮校准工具
专门用于校准确认对话框中的"确定"按钮位置
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pyautogui
import json
import os
import time
import cv2
import numpy as np
from typing import Dict, Tuple, Optional

class ConfirmButtonCalibrator:
    """确认按钮校准器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("确认按钮校准工具")
        self.root.geometry("500x600")
        
        # 确认按钮配置
        self.confirm_buttons = {
            'dialog_confirm_button': {
                'name': '对话框确定按钮',
                'description': '确认下单对话框中的确定按钮',
                'x': 0.52,  # 基于您的截图估算
                'y': 0.54
            }
        }
        
        self.current_pos = None
        self.screenshot = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="确认按钮校准工具", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 说明
        info_text = """使用说明：
1. 打开景陶易购客户端
2. 手动触发一个确认对话框（比如下单）
3. 当对话框出现时，点击"开始校准"
4. 点击对话框中的"确定"按钮位置
5. 保存校准结果"""
        
        info_frame = ttk.LabelFrame(main_frame, text="操作指南", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
        info_label.pack()
        
        # 当前状态
        self.status_var = tk.StringVar(value="准备就绪，请先触发确认对话框")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, foreground="blue")
        status_label.pack(pady=(0, 10))
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 截图按钮
        screenshot_btn = ttk.Button(button_frame, text="截取屏幕", command=self.take_screenshot)
        screenshot_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 校准按钮
        calibrate_btn = ttk.Button(button_frame, text="开始校准", command=self.start_calibration)
        calibrate_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 测试按钮
        test_btn = ttk.Button(button_frame, text="测试点击", command=self.test_click)
        test_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 坐标显示
        coord_frame = ttk.LabelFrame(main_frame, text="坐标信息", padding="10")
        coord_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 屏幕坐标
        ttk.Label(coord_frame, text="屏幕坐标:").grid(row=0, column=0, sticky=tk.W)
        self.screen_x_var = tk.StringVar(value="0")
        self.screen_y_var = tk.StringVar(value="0")
        ttk.Entry(coord_frame, textvariable=self.screen_x_var, width=10).grid(row=0, column=1, padx=(5, 0))
        ttk.Entry(coord_frame, textvariable=self.screen_y_var, width=10).grid(row=0, column=2, padx=(5, 0))
        
        # 相对坐标
        ttk.Label(coord_frame, text="相对坐标:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.rel_x_var = tk.StringVar(value="0.52")
        self.rel_y_var = tk.StringVar(value="0.54")
        ttk.Entry(coord_frame, textvariable=self.rel_x_var, width=10).grid(row=1, column=1, padx=(5, 0), pady=(10, 0))
        ttk.Entry(coord_frame, textvariable=self.rel_y_var, width=10).grid(row=1, column=2, padx=(5, 0), pady=(10, 0))
        
        # 保存按钮
        save_frame = ttk.Frame(main_frame)
        save_frame.pack(fill=tk.X, pady=(0, 20))
        
        save_btn = ttk.Button(save_frame, text="保存配置", command=self.save_config)
        save_btn.pack()
        
        # 自动检测区域
        auto_frame = ttk.LabelFrame(main_frame, text="自动检测", padding="10")
        auto_frame.pack(fill=tk.X, pady=(0, 20))
        
        auto_detect_btn = ttk.Button(auto_frame, text="自动检测确认按钮", command=self.auto_detect_button)
        auto_detect_btn.pack(pady=(0, 10))
        
        auto_info = ttk.Label(auto_frame, text="自动检测功能会尝试识别屏幕上的确认按钮", font=("Arial", 9))
        auto_info.pack()
    
    def take_screenshot(self):
        """截取屏幕"""
        try:
            self.status_var.set("正在截取屏幕...")
            self.root.withdraw()  # 隐藏窗口
            time.sleep(0.5)
            
            screenshot = pyautogui.screenshot()
            self.screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # 保存截图
            timestamp = int(time.time())
            filename = f"screenshot_for_calibration_{timestamp}.png"
            cv2.imwrite(filename, self.screenshot)
            
            self.root.deiconify()  # 显示窗口
            self.status_var.set(f"截图已保存: {filename}")
            
        except Exception as e:
            self.root.deiconify()
            self.status_var.set(f"截图失败: {e}")
    
    def start_calibration(self):
        """开始校准"""
        try:
            self.status_var.set("校准模式：请点击确认对话框中的确定按钮")
            
            # 隐藏校准工具窗口
            self.root.withdraw()
            
            # 等待用户点击
            messagebox.showinfo("校准提示", 
                              "请在确认对话框中点击'确定'按钮的位置\n"
                              "点击确定后，您有3秒时间去点击确定按钮位置")
            
            # 3秒倒计时
            for i in range(3, 0, -1):
                print(f"倒计时: {i}秒")
                time.sleep(1)
            
            # 获取鼠标位置
            current_pos = pyautogui.position()
            self.current_pos = current_pos
            
            # 显示窗口
            self.root.deiconify()
            
            # 更新坐标显示
            self.screen_x_var.set(str(current_pos.x))
            self.screen_y_var.set(str(current_pos.y))
            
            # 计算相对坐标（基于屏幕大小）
            screen_width, screen_height = pyautogui.size()
            rel_x = current_pos.x / screen_width
            rel_y = current_pos.y / screen_height
            
            self.rel_x_var.set(f"{rel_x:.4f}")
            self.rel_y_var.set(f"{rel_y:.4f}")
            
            self.status_var.set(f"校准完成: ({current_pos.x}, {current_pos.y})")
            
        except Exception as e:
            self.root.deiconify()
            self.status_var.set(f"校准失败: {e}")
    
    def test_click(self):
        """测试点击"""
        try:
            if self.current_pos:
                self.status_var.set("测试点击中...")
                self.root.withdraw()
                time.sleep(0.5)
                
                pyautogui.click(self.current_pos.x, self.current_pos.y)
                
                time.sleep(1)
                self.root.deiconify()
                self.status_var.set("测试点击完成")
            else:
                self.status_var.set("请先进行校准")
                
        except Exception as e:
            self.root.deiconify()
            self.status_var.set(f"测试点击失败: {e}")
    
    def auto_detect_button(self):
        """自动检测确认按钮"""
        try:
            self.status_var.set("正在自动检测确认按钮...")
            
            # 截取当前屏幕
            screenshot = pyautogui.screenshot()
            img = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # 检测确认按钮的可能位置
            possible_positions = self._detect_confirm_button(img)
            
            if possible_positions:
                # 使用第一个检测到的位置
                best_pos = possible_positions[0]
                
                self.screen_x_var.set(str(best_pos[0]))
                self.screen_y_var.set(str(best_pos[1]))
                
                # 计算相对坐标
                screen_width, screen_height = pyautogui.size()
                rel_x = best_pos[0] / screen_width
                rel_y = best_pos[1] / screen_height
                
                self.rel_x_var.set(f"{rel_x:.4f}")
                self.rel_y_var.set(f"{rel_y:.4f}")
                
                self.current_pos = pyautogui.Point(best_pos[0], best_pos[1])
                self.status_var.set(f"自动检测完成: 找到 {len(possible_positions)} 个可能位置")
            else:
                self.status_var.set("未检测到确认按钮，请手动校准")
                
        except Exception as e:
            self.status_var.set(f"自动检测失败: {e}")
    
    def _detect_confirm_button(self, img: np.ndarray) -> list:
        """检测确认按钮的可能位置"""
        try:
            possible_positions = []
            height, width = img.shape[:2]
            
            # 方法1: 基于颜色检测（确定按钮通常是蓝色或其他突出颜色）
            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            
            # 检测蓝色按钮（确定按钮常见颜色）
            blue_lower = np.array([100, 50, 50])
            blue_upper = np.array([130, 255, 255])
            blue_mask = cv2.inRange(hsv, blue_lower, blue_upper)
            
            # 查找轮廓
            contours, _ = cv2.findContours(blue_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if 500 < area < 5000:  # 按钮大小过滤
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # 宽高比检查（按钮通常是矩形）
                    aspect_ratio = w / h
                    if 1.5 < aspect_ratio < 4.0:
                        center_x = x + w // 2
                        center_y = y + h // 2
                        possible_positions.append((center_x, center_y))
            
            # 方法2: 基于位置推测（对话框通常在屏幕中央，确定按钮在右侧）
            if not possible_positions:
                center_x, center_y = width // 2, height // 2
                
                # 基于您的截图，确定按钮可能在这些位置
                estimated_positions = [
                    (center_x + 50, center_y + 20),   # 中央偏右下
                    (center_x + 80, center_y + 30),   # 更右一点
                    (center_x + 30, center_y + 25),   # 稍微偏右
                ]
                
                possible_positions.extend(estimated_positions)
            
            return possible_positions[:3]  # 返回最多3个位置
            
        except Exception as e:
            print(f"检测确认按钮失败: {e}")
            return []
    
    def save_config(self):
        """保存配置"""
        try:
            if not self.current_pos:
                messagebox.showwarning("警告", "请先进行校准")
                return
            
            # 读取现有配置
            config_file = "smart_coordinates_config.json"
            config = {}
            
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            # 确保button_positions存在
            if 'button_positions' not in config:
                config['button_positions'] = {}
            
            # 添加确认按钮配置
            config['button_positions']['dialog_confirm_button'] = {
                'name': '对话框确定按钮',
                'x': float(self.rel_x_var.get()),
                'y': float(self.rel_y_var.get()),
                'description': '确认下单对话框中的确定按钮',
                'screen_x': int(self.screen_x_var.get()),
                'screen_y': int(self.screen_y_var.get())
            }
            
            # 保存配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            self.status_var.set(f"配置已保存到: {config_file}")
            messagebox.showinfo("成功", "确认按钮配置已保存！")
            
        except Exception as e:
            self.status_var.set(f"保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def run(self):
        """运行校准工具"""
        self.root.mainloop()

def main():
    """主函数"""
    print("启动确认按钮校准工具...")
    calibrator = ConfirmButtonCalibrator()
    calibrator.run()

if __name__ == "__main__":
    main()