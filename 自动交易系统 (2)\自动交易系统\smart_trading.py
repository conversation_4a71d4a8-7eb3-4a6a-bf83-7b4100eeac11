#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能交易系统
直接操作景陶易购客户端，无需坐标定位
"""

import subprocess
import time
import logging
import pyautogui
import cv2
import numpy as np
from typing import Dict, Optional
import os
import sys
import random
import hashlib

# 导入修复的颜色检测器
try:
    from color_detection_fix import JingTaoColorDetector
    COLOR_DETECTION_AVAILABLE = True
except ImportError:
    COLOR_DETECTION_AVAILABLE = False
    JingTaoColorDetector = None

# 导入自动确认处理器
try:
    from auto_confirm_handler import AutoConfirmHandler
    AUTO_CONFIRM_AVAILABLE = True
except ImportError:
    AUTO_CONFIRM_AVAILABLE = False
    AutoConfirmHandler = None

# 修复secrets模块问题
try:
    import secrets
except ImportError:
    # 创建secrets模块的替代实现
    class SecretsModule:
        """Secrets模块的替代实现"""
        
        def __init__(self):
            self._random = random.Random()
            self._random.seed(int(time.time() * 1000000))
        
        def token_bytes(self, nbytes=None):
            """生成随机字节"""
            if nbytes is None:
                nbytes = 32
            return bytes(self._random.getrandbits(8) for _ in range(nbytes))
        
        def token_hex(self, nbytes=None):
            """生成随机十六进制字符串"""
            return self.token_bytes(nbytes).hex()
        
        def token_urlsafe(self, nbytes=None):
            """生成URL安全的随机字符串"""
            import base64
            return base64.urlsafe_b64encode(self.token_bytes(nbytes)).decode('ascii').rstrip('=')
        
        def choice(self, sequence):
            """从序列中随机选择"""
            return self._random.choice(sequence)
        
        def randbelow(self, n):
            """生成小于n的随机整数"""
            return self._random.randrange(n)
        
        def randbits(self, k):
            """生成k位随机整数"""
            return self._random.getrandbits(k)
    
    # 将替代实现添加到sys.modules
    sys.modules['secrets'] = SecretsModule()
    secrets = sys.modules['secrets']

from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout
from PyQt5.QtWidgets import QPushButton, QLabel, QTextEdit, QGroupBox, QMessageBox
from PyQt5.QtCore import QTimer, QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont
import json
import tkinter as tk
from tkinter import messagebox, simpledialog

# 导入资源路径处理模块
try:
    from utils.resource_path import get_resource_path, get_config_path, get_data_path, get_log_path, config_manager
    RESOURCE_PATH_AVAILABLE = True
except ImportError:
    RESOURCE_PATH_AVAILABLE = False
    config_manager = None

# 导入增强检测模块
try:
    from core.enhanced_detection import enhanced_detector, SignalType
    ENHANCED_DETECTION_AVAILABLE = True
except ImportError:
    ENHANCED_DETECTION_AVAILABLE = False
    enhanced_detector = None
    SignalType = None

# 安全导入pytesseract
try:
    import pytesseract
    TESSERACT_AVAILABLE = True
    # 设置Tesseract路径
    tesseract_paths = [
        r"D:\Program Files\tesseract.exe",
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe".format(os.getenv('USERNAME', '')),
        "tesseract"  # 系统PATH中的tesseract
    ]

    for path in tesseract_paths:
        if os.path.exists(path) or path == "tesseract":
            try:
                pytesseract.pytesseract.tesseract_cmd = path
                # 测试是否可用
                pytesseract.get_tesseract_version()
                break
            except:
                continue
    else:
        logging.warning("未找到可用的Tesseract安装，OCR功能将不可用")
        TESSERACT_AVAILABLE = False

except ImportError:
    logging.warning("pytesseract未安装，OCR功能将不可用")
    TESSERACT_AVAILABLE = False
    pytesseract = None

class SmartTradingEngine:
    """智能交易引擎"""
    
    def __init__(self, client_path: str = None):
        self.logger = logging.getLogger(__name__)
        
        # 客户端路径
        self.client_path = client_path or r"D:\Program Files\DEAL_JCST_RNHY_Client"
        self.client_process = None
        self.client_window = None
        self.window_rect = None
        
        # 智能坐标定位 - 从配置文件加载
        self.relative_positions = self.load_coordinate_config()
        
        # 监测数据
        self.price_history = []
        self.yellow_line_data = []
        self.last_trade_time = 0
        
        # 交易参数
        self.trade_cooldown = 30  # 交易冷却时间
        self.min_price_change = 0.5  # 最小价格变化率
        self.is_running = False
        
        # 新增：黄线检测区域
        self.yellow_line_region = None  # (x, y, width, height)
        
        # 新增：转让冷却期和货值检测
        self.last_transfer_time = 0
        self.transfer_cooldown = 60  # 转让冷却时间（秒）
        self.last_profit_value = None
        self.profit_change_threshold = 1.0  # 货值变化阈值（调整为1.0，更敏感）
        
        # 初始化景陶易购专用颜色检测器
        self.jingtao_detector = None
        if COLOR_DETECTION_AVAILABLE:
            try:
                self.jingtao_detector = JingTaoColorDetector()
                self.logger.info("✅ 景陶易购专用颜色检测器初始化成功")
            except Exception as e:
                self.logger.error(f"❌ 景陶易购专用颜色检测器初始化失败: {e}")
        else:
            self.logger.warning("⚠️ 颜色检测器不可用，将使用备用检测方法")
        
        # 初始化自动确认处理器
        self.auto_confirm_handler = None
        if AUTO_CONFIRM_AVAILABLE:
            try:
                self.auto_confirm_handler = AutoConfirmHandler()
                self.logger.info("✅ 自动确认处理器初始化成功")
            except Exception as e:
                self.logger.error(f"❌ 自动确认处理器初始化失败: {e}")
        else:
            self.logger.warning("⚠️ 自动确认处理器不可用")
        
        # 验证坐标配置
        self.validate_coordinates()
        
        self.logger.info("智能交易引擎初始化完成")
    
    def load_coordinate_config(self) -> dict:
        """加载坐标配置文件"""
        try:
            config_file = "smart_coordinates_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                if 'button_positions' in config_data:
                    button_positions = {}
                    for key, value in config_data['button_positions'].items():
                        # 使用calibrated_absolute坐标
                        if 'calibrated_absolute' in value:
                            abs_x = value['calibrated_absolute']['x']
                            abs_y = value['calibrated_absolute']['y']
                            # 计算相对坐标
                            if 'window_info' in config_data and 'rect' in config_data['window_info']:
                                rect = config_data['window_info']['rect']
                                rel_x = (abs_x - rect['left']) / rect['width']
                                rel_y = (abs_y - rect['top']) / rect['height']
                                button_positions[key] = (rel_x, rel_y)
                            else:
                                # 如果没有窗口信息,使用配置文件中的相对坐标
                                button_positions[key] = (value['x'], value['y'])
                        else:
                            # 使用配置文件中的相对坐标
                            button_positions[key] = (value['x'], value['y'])
                    
                    # 同时保存窗口信息用于调试
                    if 'window_info' in config_data:
                        self.config_window_info = config_data['window_info']
                        self.logger.info(f"已加载窗口信息: {self.config_window_info.get('title', 'Unknown')}")
                    
                    self.logger.info(f"✅ 已加载最新坐标配置: {len(button_positions)} 个按钮")
                    
                    # 显示详细的坐标信息
                    for key, (x, y) in button_positions.items():
                        button_info = config_data['button_positions'][key]
                        abs_pos = button_info.get('calibrated_absolute', {})
                        self.logger.info(f"  - {button_info.get('name', key)}: 相对({x:.4f}, {y:.4f}) "
                                       f"绝对({abs_pos.get('x', 'N/A')}, {abs_pos.get('y', 'N/A')})")
                    
                    return button_positions
                else:
                    self.logger.warning("配置文件中未找到button_positions")
            else:
                self.logger.info("未找到坐标配置文件，使用默认配置")
        except Exception as e:
            self.logger.error(f"加载坐标配置失败: {e}")
        
        # 默认配置 - 使用配置文件中的精确坐标
        default_positions = {
            'buy_mode_button': (0.01395348837209302, 0.7064516129032258),      # 买入模式按钮
            'sell_mode_button': (0.014341085271317829, 0.7819354838709677),   # 卖出模式按钮
            'buy_order_button': (0.07829457364341085, 0.9270967741935484),    # 买入订立按钮
            'sell_order_button': (0.07946511627906976, 0.9296774193548387),   # 卖出订立按钮
            'price_input': (0.14263566463414634, 0.7593548387096774),         # 价格输入框
            'quantity_input': (0.14263566463414634, 0.7974193548387097),      # 数量输入框
            'confirm_button': (0.43488372093023255, 0.5419354838709677),      # 确认按钮
            'transfer_out_button': (0.11821705426356589, 0.7251612903225806), # 转出按钮
            'order_mode_button': (0.05968992248062015, 0.7283870967741935)    # 订立模式按钮
        }
        
        self.logger.info("⚠️ 使用内置的精确坐标配置")
        return default_positions
    
    def start_client(self) -> bool:
        """检测客户端是否已运行并校准按钮位置"""
        try:
            # 检查客户端是否已经在运行
            self.logger.info("检测景陶易购客户端...")
            
            # 方法1: 通过进程名检测
            import psutil
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if '景陶易购' in proc.info['name'] or 'DEAL_JCST' in proc.info['name']:
                        self.logger.info(f"找到运行中的客户端进程: {proc.info['name']} (PID: {proc.info['pid']})")
                        self.client_process = proc
                        break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # 查找客户端窗口
            if not self.find_client_window():
                self.logger.warning("未检测到景陶易购客户端，请先手动启动客户端")
                return False
            
            # 比较窗口尺寸
            self.compare_window_dimensions()
            
            # 进行按钮校准
            if not self.calibrate_buttons():
                self.logger.error("按钮校准失败")
                return False
            
            # 暂时跳过区域选择，直接使用默认区域
            if self.window_rect:
                self.yellow_line_region = (0, 0, self.window_rect.width // 2, self.window_rect.height // 2)
                self.logger.info(f"使用默认黄线检测区域: {self.yellow_line_region}")
            
            self.logger.info("客户端检测和按钮校准完成")
            return True
                
        except Exception as e:
            self.logger.error(f"检测客户端失败: {e}")
            return False
    
    def calibrate_buttons(self) -> bool:
        """校准按钮位置"""
        try:
            self.logger.info("开始按钮校准...")
            
            # 激活客户端窗口
            if self.client_window:
                self.client_window.activate()
                time.sleep(2)
                self.logger.info("客户端窗口已激活")
            
            # 通过图像识别查找按钮
            detected_buttons = self.find_buttons_by_image()
            
            if detected_buttons:
                # 更新相对位置
                for button_name, (abs_x, abs_y) in detected_buttons.items():
                    if self.window_rect:
                        rel_x = (abs_x - self.window_rect.left) / self.window_rect.width
                        rel_y = (abs_y - self.window_rect.top) / self.window_rect.height
                        self.relative_positions[button_name] = (rel_x, rel_y)
                        self.logger.info(f"✅ 校准 {button_name}: 相对位置 ({rel_x:.3f}, {rel_y:.3f})")
                
                # 保存校准结果
                self.save_calibration_result()
                
                # 测试校准后的坐标
                self.test_coordinates()
                
                self.logger.info("✅ 按钮校准完成")
                return True
            else:
                self.logger.warning("⚠️ 未通过图像识别找到按钮，使用默认相对位置")
                
                # 即使使用默认位置也要测试坐标
                self.test_coordinates()
                
                return True  # 使用默认位置继续
            
        except Exception as e:
            self.logger.error(f"按钮校准失败: {e}")
            return False
    
    def select_yellow_line_region(self):
        """选择黄线检测区域"""
        try:
            self.logger.info("开始选择黄线检测区域...")
            
            if not self.client_window:
                self.logger.error("客户端窗口未找到")
                return False
            
            # 激活客户端窗口
            self.client_window.activate()
            time.sleep(1)
            
            # 截取整个客户端窗口
            if self.window_rect:
                full_screen = pyautogui.screenshot(region=(
                    self.window_rect.left,
                    self.window_rect.top,
                    self.window_rect.width,
                    self.window_rect.height
                ))
                
                # 保存完整截图供用户参考
                full_screenshot_filename = f"full_client_screenshot_{int(time.time())}.png"
                full_screen.save(full_screenshot_filename)
                self.logger.info(f"✅ 保存完整客户端截图: {full_screenshot_filename}")
                
                # 显示选择提示
                self.logger.info("请按照以下步骤选择黄线检测区域：")
                self.logger.info("1. 查看完整截图，确定黄线位置")
                self.logger.info("2. 使用鼠标框选黄线区域")
                self.logger.info("3. 按Enter确认选择，按Esc取消")
                
                # 等待用户选择区域
                root = tk.Tk()
                root.withdraw()  # 隐藏主窗口
                
                # 显示选择对话框
                result = messagebox.askyesno(
                    "区域选择",
                    "是否要手动选择黄线检测区域？\n\n"
                    "选择'是'：将打开区域选择工具\n"
                    "选择'否'：使用默认区域（左侧K线图）"
                )
                
                if result:
                    # 使用区域选择工具
                    region = self._select_region_interactive()
                    if region:
                        self.yellow_line_region = region
                        self.logger.info(f"✅ 已选择黄线检测区域: {region}")
                        self.save_yellow_line_region()
                        return True
                    else:
                        self.logger.info("用户取消了区域选择")
                        return False
                else:
                    # 使用默认区域
                    self.yellow_line_region = (0, 0, self.window_rect.width // 2, self.window_rect.height // 2)
                    self.logger.info(f"使用默认黄线检测区域: {self.yellow_line_region}")
                    return True
            else:
                self.logger.error("无法获取窗口位置")
                return False
                
        except Exception as e:
            self.logger.error(f"选择黄线检测区域失败: {e}")
            return False
    
    def _select_region_interactive(self):
        """交互式区域选择"""
        try:
            root = tk.Tk()
            root.title("黄线检测区域选择")
            root.geometry("400x300")
            
            # 显示说明
            label = tk.Label(root, text="请选择黄线检测区域：\n\n"
                              "1. 点击'选择区域'按钮\n"
                              "2. 在客户端窗口上拖拽选择黄线区域\n"
                              "3. 选择完成后点击'确认'", 
                              justify=tk.LEFT, pady=20)
            label.pack()
            
            selected_region = None
            
            def select_region():
                nonlocal selected_region
                root.withdraw()  # 隐藏选择窗口
                
                # 等待用户选择
                time.sleep(1)
                
                # 手动输入区域坐标
                coords = simpledialog.askstring("输入坐标", 
                    "请输入黄线区域坐标 (x,y,width,height):\n"
                    "例如: 100,200,500,300")
                if coords:
                    try:
                        x, y, w, h = map(int, coords.split(','))
                        selected_region = (x, y, w, h)
                    except:
                        messagebox.showerror("错误", "坐标格式错误")
                
                root.deiconify()  # 重新显示窗口
            
            def confirm():
                if selected_region:
                    root.destroy()
                else:
                    messagebox.showwarning("警告", "请先选择区域")
            
            def cancel():
                root.destroy()
            
            # 按钮
            button_frame = tk.Frame(root)
            button_frame.pack(pady=20)
            
            tk.Button(button_frame, text="选择区域", command=select_region).pack(side=tk.LEFT, padx=10)
            tk.Button(button_frame, text="确认", command=confirm).pack(side=tk.LEFT, padx=10)
            tk.Button(button_frame, text="取消", command=cancel).pack(side=tk.LEFT, padx=10)
            
            root.mainloop()
            
            return selected_region
            
        except Exception as e:
            self.logger.error(f"交互式区域选择失败: {e}")
            return None
    
    def save_yellow_line_region(self):
        """保存黄线检测区域配置"""
        try:
            if self.yellow_line_region:
                config_data = {
                    'yellow_line_region': {
                        'x': self.yellow_line_region[0],
                        'y': self.yellow_line_region[1],
                        'width': self.yellow_line_region[2],
                        'height': self.yellow_line_region[3]
                    }
                }
                
                config_file = "yellow_line_region_config.json"
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)
                
                self.logger.info(f"✅ 黄线检测区域已保存到: {config_file}")
            
        except Exception as e:
            self.logger.error(f"保存黄线检测区域失败: {e}")
    
    def load_yellow_line_region(self):
        """加载黄线检测区域配置"""
        try:
            config_file = "yellow_line_region_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                if 'yellow_line_region' in config_data:
                    region_data = config_data['yellow_line_region']
                    self.yellow_line_region = (
                        region_data['x'],
                        region_data['y'],
                        region_data['width'],
                        region_data['height']
                    )
                    self.logger.info(f"✅ 已加载黄线检测区域: {self.yellow_line_region}")
                    return True
                else:
                    self.logger.warning("配置文件中未找到yellow_line_region")
            else:
                self.logger.info("未找到黄线检测区域配置文件")
        except Exception as e:
            self.logger.error(f"加载黄线检测区域失败: {e}")
        
        return False
    
    def save_calibration_result(self):
        """保存校准结果"""
        try:
            import datetime
            
            # 创建配置数据
            window_info = {}
            if self.client_window:
                window_info['title'] = self.client_window.title
                if self.window_rect:
                    try:
                        window_info['rect'] = self.window_rect._asdict()
                    except AttributeError:
                        window_info['rect'] = {
                            'left': self.window_rect.left,
                            'top': self.window_rect.top,
                            'width': self.window_rect.width,
                            'height': self.window_rect.height,
                            'right': self.window_rect.right,
                            'bottom': self.window_rect.bottom
                        }
            
            button_positions = {}
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            for key, (x, y) in self.relative_positions.items():
                # 计算绝对坐标用于调试
                abs_x, abs_y = None, None
                if self.window_rect:
                    abs_x = int(self.window_rect.left + self.window_rect.width * x)
                    abs_y = int(self.window_rect.top + self.window_rect.height * y)
                
                button_positions[key] = {
                    'name': self.get_button_name(key),
                    'x': x,
                    'y': y,
                    'calibrated_absolute': {
                        'x': abs_x,
                        'y': abs_y
                    } if abs_x is not None else {},
                    'description': self.get_button_description(key),
                    'last_calibrated': current_time
                }
            
            config_data = {
                'window_info': window_info,
                'button_positions': button_positions
            }
            
            # 保存到文件
            config_file = "smart_coordinates_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"✅ 校准结果已保存到: {config_file}")
            
        except Exception as e:
            self.logger.error(f"保存校准结果失败: {e}")
    
    def get_button_name(self, button_key: str) -> str:
        """获取按钮名称"""
        button_names = {
            'buy_mode_button': '买入模式按钮',
            'sell_mode_button': '卖出模式按钮',
            'buy_order_button': '买入订立按钮',
            'sell_order_button': '卖出订立按钮',
            'price_input': '价格输入框',
            'quantity_input': '数量输入框',
            'confirm_button': '确认按钮',
            'transfer_out_button': '转出按钮',
            'order_mode_button': '订立模式按钮'
        }
        return button_names.get(button_key, button_key)
    
    def get_button_description(self, button_key: str) -> str:
        """获取按钮描述"""
        button_descriptions = {
            'buy_mode_button': '左侧红色的\'买\'按钮',
            'sell_mode_button': '左侧绿色的\'卖\'按钮',
            'buy_order_button': '底部的\'买入订立\'按钮',
            'sell_order_button': '底部的\'卖出订立\'按钮',
            'price_input': '买价输入框',
            'quantity_input': '买量输入框',
            'confirm_button': '弹出对话框中的\'确定\'按钮',
            'transfer_out_button': '转出按钮',
            'order_mode_button': '订立模式按钮'
        }
        return button_descriptions.get(button_key, f'{button_key} 按钮')
    
    def validate_coordinates(self):
        """验证坐标配置的有效性"""
        try:
            self.logger.info("🔍 验证坐标配置...")
            
            required_buttons = [
                'buy_mode_button', 'sell_mode_button', 'buy_order_button', 
                'sell_order_button', 'price_input', 'quantity_input', 
                'confirm_button', 'transfer_out_button'
            ]
            
            missing_buttons = []
            invalid_coordinates = []
            
            for button in required_buttons:
                if button not in self.relative_positions:
                    missing_buttons.append(button)
                else:
                    x, y = self.relative_positions[button]
                    # 检查坐标是否在有效范围内 (0-1)
                    if not (0 <= x <= 1 and 0 <= y <= 1):
                        invalid_coordinates.append(f"{button}: ({x:.4f}, {y:.4f})")
            
            if missing_buttons:
                self.logger.warning(f"⚠️ 缺少按钮配置: {', '.join(missing_buttons)}")
            
            if invalid_coordinates:
                self.logger.warning(f"⚠️ 无效坐标 (超出0-1范围): {'; '.join(invalid_coordinates)}")
            
            if not missing_buttons and not invalid_coordinates:
                self.logger.info("✅ 坐标配置验证通过")
            else:
                self.logger.warning("⚠️ 坐标配置存在问题，可能影响交易执行")
                
        except Exception as e:
            self.logger.error(f"❌ 坐标验证失败: {e}")
    
    def compare_window_dimensions(self):
        """比较当前窗口与配置文件中的窗口尺寸"""
        try:
            if not hasattr(self, 'config_window_info') or not self.window_rect:
                return
            
            config_rect = self.config_window_info.get('rect', {})
            current_rect = {
                'left': self.window_rect.left,
                'top': self.window_rect.top,
                'width': self.window_rect.width,
                'height': self.window_rect.height
            }
            
            self.logger.info("📐 窗口尺寸对比:")
            self.logger.info(f"  配置文件: {config_rect.get('width', 'N/A')}x{config_rect.get('height', 'N/A')} "
                           f"位置: ({config_rect.get('left', 'N/A')}, {config_rect.get('top', 'N/A')})")
            self.logger.info(f"  当前窗口: {current_rect['width']}x{current_rect['height']} "
                           f"位置: ({current_rect['left']}, {current_rect['top']})")
            
            # 检查尺寸差异
            if config_rect.get('width') and config_rect.get('height'):
                width_diff = abs(current_rect['width'] - config_rect['width'])
                height_diff = abs(current_rect['height'] - config_rect['height'])
                
                if width_diff > 50 or height_diff > 50:
                    self.logger.warning(f"⚠️ 窗口尺寸变化较大: 宽度差异{width_diff}px, 高度差异{height_diff}px")
                    self.logger.warning("   建议重新校准按钮位置以确保准确性")
                else:
                    self.logger.info("✅ 窗口尺寸变化在可接受范围内")
                    
        except Exception as e:
            self.logger.error(f"❌ 窗口尺寸对比失败: {e}")
    
    def test_coordinates(self, button_list: list = None):
        """测试坐标准确性 - 在屏幕上显示计算出的坐标位置"""
        try:
            if button_list is None:
                button_list = ['buy_mode_button', 'sell_mode_button', 'price_input', 'quantity_input']
            
            self.logger.info("🎯 测试坐标准确性...")
            
            if not self.window_rect:
                self.logger.error("窗口位置未设置，无法测试坐标")
                return False
            
            test_results = {}
            
            for button_name in button_list:
                if button_name in self.relative_positions:
                    abs_x, abs_y = self.calculate_button_position(button_name)
                    if abs_x is not None and abs_y is not None:
                        # 检查坐标是否在窗口范围内
                        within_window = (
                            self.window_rect.left <= abs_x <= self.window_rect.right and
                            self.window_rect.top <= abs_y <= self.window_rect.bottom
                        )
                        
                        test_results[button_name] = {
                            'position': (abs_x, abs_y),
                            'within_window': within_window,
                            'name': self.get_button_name(button_name)
                        }
                        
                        status = "✅" if within_window else "❌"
                        self.logger.info(f"  {status} {self.get_button_name(button_name)}: ({abs_x}, {abs_y}) "
                                       f"{'在窗口内' if within_window else '超出窗口范围'}")
                    else:
                        test_results[button_name] = {
                            'position': None,
                            'within_window': False,
                            'name': self.get_button_name(button_name)
                        }
                        self.logger.error(f"  ❌ {self.get_button_name(button_name)}: 坐标计算失败")
                else:
                    self.logger.warning(f"  ⚠️ {button_name}: 配置中未找到")
            
            # 统计结果
            valid_count = sum(1 for result in test_results.values() 
                            if result['position'] is not None and result['within_window'])
            total_count = len(test_results)
            
            self.logger.info(f"📊 坐标测试结果: {valid_count}/{total_count} 个按钮坐标有效")
            
            if valid_count == total_count:
                self.logger.info("✅ 所有测试的按钮坐标都有效")
                return True
            else:
                self.logger.warning(f"⚠️ 有 {total_count - valid_count} 个按钮坐标无效，可能影响交易执行")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 坐标测试失败: {e}")
            return False
    
    def find_client_window(self) -> bool:
        """查找客户端窗口"""
        try:
            # 方法1: 优先查找DEAL_JCST_RNHY_Client窗口
            all_windows = pyautogui.getAllWindows()
            for window in all_windows:
                if "DEAL_JCST_RNHY_Client" in window.title:
                    # 检查窗口是否最小化（位置为-32000, -32000）
                    if window.left == -32000 and window.top == -32000:
                        self.logger.info(f"跳过最小化的窗口: {window.title}")
                        continue
                    
                    self.client_window = window
                    try:
                        self.window_rect = self.client_window.rect
                    except AttributeError:
                        # 手动构造rect对象
                        from collections import namedtuple
                        Rect = namedtuple('Rect', ['left', 'top', 'width', 'height', 'right', 'bottom'])
                        self.window_rect = Rect(
                            left=self.client_window.left,
                            top=self.client_window.top,
                            width=self.client_window.width,
                            height=self.client_window.height,
                            right=self.client_window.left + self.client_window.width,
                            bottom=self.client_window.top + self.client_window.height
                        )
                    self.logger.info(f"找到客户端窗口: {self.client_window.title}")
                    self.logger.info(f"窗口位置: {self.window_rect}")
                    return True
            
            # 方法2: 通过窗口标题查找
            windows = pyautogui.getWindowsWithTitle("景陶易购")
            if windows:
                for window in windows:
                    # 排除智能交易系统窗口和最小化窗口
                    if ("智能交易系统" not in window.title and 
                        not (window.left == -32000 and window.top == -32000)):
                        self.client_window = window
                        try:
                            self.window_rect = self.client_window.rect
                        except AttributeError:
                            # 手动构造rect对象
                            from collections import namedtuple
                            Rect = namedtuple('Rect', ['left', 'top', 'width', 'height', 'right', 'bottom'])
                            self.window_rect = Rect(
                                left=self.client_window.left,
                                top=self.client_window.top,
                                width=self.client_window.width,
                                height=self.client_window.height,
                                right=self.client_window.left + self.client_window.width,
                                bottom=self.client_window.top + self.client_window.height
                            )
                        self.logger.info(f"找到客户端窗口: {self.client_window.title}")
                        self.logger.info(f"窗口位置: {self.window_rect}")
                        return True
            
            # 方法3: 通过关键词查找
            for window in all_windows:
                # 排除智能交易系统窗口和最小化窗口
                if ("智能交易系统" in window.title or 
                    (window.left == -32000 and window.top == -32000)):
                    continue
                    
                if any(keyword in window.title for keyword in ['景陶易购', 'DEAL', 'JCST', 'RNHY', '五福临门']):
                    self.client_window = window
                    try:
                        self.window_rect = self.client_window.rect
                    except AttributeError:
                        # 手动构造rect对象
                        from collections import namedtuple
                        Rect = namedtuple('Rect', ['left', 'top', 'width', 'height', 'right', 'bottom'])
                        self.window_rect = Rect(
                            left=self.client_window.left,
                            top=self.client_window.top,
                            width=self.client_window.width,
                            height=self.client_window.height,
                            right=self.client_window.left + self.client_window.width,
                            bottom=self.client_window.top + self.client_window.height
                        )
                    self.logger.info(f"找到相关客户端窗口: {window.title}")
                    self.logger.info(f"窗口位置: {self.window_rect}")
                    return True
            
            self.logger.warning("未找到客户端窗口")
            return False
                
        except Exception as e:
            self.logger.error(f"查找客户端窗口失败: {e}")
            return False
    
    def calculate_button_position(self, button_name: str) -> tuple:
        """根据窗口大小计算按钮绝对坐标"""
        try:
            if not self.window_rect:
                raise ValueError("窗口位置未设置")
            
            if button_name not in self.relative_positions:
                raise ValueError(f"未知的按钮名称: {button_name}")
            
            relative_x, relative_y = self.relative_positions[button_name]
            
            # 计算绝对坐标
            abs_x = int(self.window_rect.left + self.window_rect.width * relative_x)
            abs_y = int(self.window_rect.top + self.window_rect.height * relative_y)
            
            # 详细的坐标计算日志
            self.logger.debug(f"🎯 坐标计算详情 - {self.get_button_name(button_name)}:")
            self.logger.debug(f"  - 窗口: ({self.window_rect.left}, {self.window_rect.top}) "
                            f"尺寸: {self.window_rect.width}x{self.window_rect.height}")
            self.logger.debug(f"  - 相对坐标: ({relative_x:.6f}, {relative_y:.6f})")
            self.logger.debug(f"  - 绝对坐标: ({abs_x}, {abs_y})")
            
            self.logger.info(f"📍 {self.get_button_name(button_name)} 位置: ({abs_x}, {abs_y})")
            return abs_x, abs_y
            
        except Exception as e:
            self.logger.error(f"❌ 计算 {button_name} 位置失败: {e}")
            return None, None
    
    def auto_calibrate_positions(self) -> bool:
        """自动校准按钮位置"""
        try:
            self.logger.info("开始自动校准按钮位置...")
            
            # 查找客户端窗口
            if not self.find_client_window():
                return False
            
            # 激活窗口
            if self.client_window:
                self.client_window.activate()
                time.sleep(1)
            
            # 通过图像识别查找按钮
            detected_buttons = self.find_buttons_by_image()
            
            if detected_buttons:
                # 更新相对位置
                for button_name, (abs_x, abs_y) in detected_buttons.items():
                    if self.window_rect:
                        rel_x = (abs_x - self.window_rect.left) / self.window_rect.width
                        rel_y = (abs_y - self.window_rect.top) / self.window_rect.height
                        self.relative_positions[button_name] = (rel_x, rel_y)
                        self.logger.info(f"校准 {button_name}: 相对位置 ({rel_x:.3f}, {rel_y:.3f})")
                
                return True
            
            self.logger.warning("未通过图像识别找到按钮，使用默认相对位置")
            return False
            
        except Exception as e:
            self.logger.error(f"自动校准失败: {e}")
            return False
    
    def find_buttons_by_image(self) -> dict:
        """通过图像识别查找按钮"""
        try:
            # 截取窗口区域
            screenshot = pyautogui.screenshot(region=(
                self.window_rect.left, self.window_rect.top,
                self.window_rect.width, self.window_rect.height
            ))
            screen_array = np.array(screenshot)
            
            # 查找按钮
            buttons = {}
            
            # 查找买入模式按钮 (左侧垂直按钮)
            buy_mode_pos = self.find_text_in_image(screen_array, "买入")
            if buy_mode_pos:
                buttons['buy_mode_button'] = (
                    self.window_rect.left + buy_mode_pos[0],
                    self.window_rect.top + buy_mode_pos[1]
                )
            
            # 查找卖出模式按钮 (左侧垂直按钮)
            sell_mode_pos = self.find_text_in_image(screen_array, "卖出")
            if sell_mode_pos:
                buttons['sell_mode_button'] = (
                    self.window_rect.left + sell_mode_pos[0],
                    self.window_rect.top + sell_mode_pos[1]
                )
            
            # 查找买入订立按钮 (底部大按钮)
            buy_order_pos = self.find_text_in_image(screen_array, "买入订立")
            if buy_order_pos:
                buttons['buy_order_button'] = (
                    self.window_rect.left + buy_order_pos[0],
                    self.window_rect.top + buy_order_pos[1]
                )
            
            # 查找卖出订立按钮 (底部大按钮)
            sell_order_pos = self.find_text_in_image(screen_array, "卖出订立")
            if sell_order_pos:
                buttons['sell_order_button'] = (
                    self.window_rect.left + sell_order_pos[0],
                    self.window_rect.top + sell_order_pos[1]
                )
            
            # 查找确认按钮
            confirm_pos = self.find_text_in_image(screen_array, "确认")
            if confirm_pos:
                buttons['confirm_button'] = (
                    self.window_rect.left + confirm_pos[0],
                    self.window_rect.top + confirm_pos[1]
                )
            
            # 查找转出按钮
            transfer_out_pos = self.find_text_in_image(screen_array, "转出")
            if transfer_out_pos:
                buttons['transfer_out_button'] = (
                    self.window_rect.left + transfer_out_pos[0],
                    self.window_rect.top + transfer_out_pos[1]
                )
            
            self.logger.info(f"通过图像识别找到 {len(buttons)} 个按钮")
            return buttons
            
        except Exception as e:
            self.logger.error(f"图像识别按钮失败: {e}")
            return {}
    
    def find_text_in_image(self, image: np.ndarray, text: str) -> tuple:
        """在图像中查找文字位置"""
        try:
            if not TESSERACT_AVAILABLE:
                self.logger.warning("Tesseract不可用，无法进行文字识别")
                return None

            # OCR识别
            data = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)

            # 查找指定文字
            for i, word in enumerate(data['text']):
                if text in word:
                    x = data['left'][i]
                    y = data['top'][i]
                    return x, y

            return None

        except Exception as e:
            self.logger.error(f"查找文字失败: {e}")
            return None
    
    def detect_yellow_line_change(self, screen):
        """检测黄线变化 - 基于角度变化判断交易信号"""
        try:
            self.logger.info("开始黄线检测...")
            
            height, width = screen.shape[:2]
            self.logger.info(f"屏幕尺寸: {width} x {height}")
            
            # 检测区域：使用用户指定的区域或默认区域
            if hasattr(self, 'yellow_line_region') and self.yellow_line_region:
                x, y, w, h = self.yellow_line_region
                chart_region = screen[y:y+h, x:x+w]
                self.logger.info(f"使用指定黄线检测区域: {self.yellow_line_region}")
            else:
                # 默认检测区域：左侧K线图区域
                chart_region = screen[0:height//2, 0:width//2]
                self.logger.info("使用默认黄线检测区域（左侧K线图）")
            
            self.logger.info(f"黄线检测区域尺寸: {chart_region.shape[1]} x {chart_region.shape[0]}")
            
            # 保存调试图像
            debug_chart_filename = f"debug_chart_region_{int(time.time())}.png"
            success = cv2.imwrite(debug_chart_filename, chart_region)
            if success:
                self.logger.info(f"✅ 保存黄线检测区域截图: {debug_chart_filename}")
            else:
                self.logger.error(f"❌ 保存黄线检测区域截图失败: {debug_chart_filename}")
            
            # 转换到HSV颜色空间
            hsv = cv2.cvtColor(chart_region, cv2.COLOR_BGR2HSV)
            
            # 尝试多种黄色范围
            yellow_ranges = [
                (np.array([10, 30, 30]), np.array([40, 255, 255])),  # 宽松范围
                (np.array([15, 50, 50]), np.array([35, 255, 255])),  # 中等范围
                (np.array([20, 100, 100]), np.array([30, 255, 255])), # 严格范围
                (np.array([5, 20, 20]), np.array([45, 255, 255]))     # 最宽松范围
            ]
            
            best_mask = None
            best_area = 0
            
            for i, (lower, upper) in enumerate(yellow_ranges):
                # 创建掩码
                mask = cv2.inRange(hsv, lower, upper)
                
                # 查找轮廓
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                if contours:
                    max_contour = max(contours, key=cv2.contourArea)
                    area = cv2.contourArea(max_contour)
                    
                    if area > best_area:
                        best_area = area
                        best_mask = mask
                        self.logger.info(f"黄色范围{i+1}找到最大面积: {area}")
            
            if best_mask is None:
                self.logger.info("未检测到黄线")
                return {'signal': 'none', 'direction': 'stable', 'area': 0, 'angle': 0.0, 'position': (0, 0)}
            
            # 使用最佳掩码
            mask = best_mask
            
            # 保存掩码图像用于调试
            debug_mask_filename = f"debug_mask_{int(time.time())}.png"
            cv2.imwrite(debug_mask_filename, mask)
            self.logger.info(f"保存掩码图像: {debug_mask_filename}")
            
            # 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            self.logger.info(f"找到 {len(contours)} 个轮廓")
            
            if not contours:
                self.logger.info("未检测到黄线")
                return {'signal': 'none', 'direction': 'stable', 'area': 0, 'angle': 0.0, 'position': (0, 0)}
            
            # 找到最大的黄色区域
            max_contour = max(contours, key=cv2.contourArea)
            area = cv2.contourArea(max_contour)
            self.logger.info(f"最大轮廓面积: {area}")
            
            if area < 5:  # 进一步降低面积阈值，从20改为5
                self.logger.info(f"黄线面积太小: {area}")
                return {'signal': 'none', 'direction': 'stable', 'area': area, 'angle': 0.0, 'position': (0, 0)}
            
            # 获取黄线位置和尺寸
            x, y, w, h = cv2.boundingRect(max_contour)
            self.logger.info(f"黄线位置: ({x}, {y}), 尺寸: {w} x {h}")
            
            # 收集黄线数据点（更精确的采样）
            yellow_points = []
            
            # 方法1：按列采样，每列取最上方的黄线点
            for i in range(0, w, 3):  # 每3个像素采样一次
                col_pixels = np.where(mask[:, x + i] > 0)[0]
                if len(col_pixels) > 0:
                    # 取每列最上方的黄线点
                    top_y = np.min(col_pixels)
                    yellow_points.append((x + i, y + top_y))
            
            # 如果方法1收集的点太少，使用方法2：密集采样
            if len(yellow_points) < 5:
                yellow_points = []
                for i in range(0, w, 2):  # 每2个像素采样一次
                    for j in range(0, h, 2):
                        if mask[y + j, x + i] > 0:
                            yellow_points.append((x + i, y + j))
            
            self.logger.info(f"收集到 {len(yellow_points)} 个黄线数据点")
            
            if len(yellow_points) < 5:  # 降低最小数据点要求
                self.logger.info("黄线数据点太少")
                return {'signal': 'none', 'direction': 'stable', 'area': area, 'angle': 0.0, 'position': (x, y)}
            
            # 按x坐标排序
            yellow_points.sort(key=lambda p: p[0])
            
            # 计算角度变化（使用最后两个数据点）
            if len(yellow_points) >= 2:
                # 获取最后两个数据点
                p1 = yellow_points[-2]
                p2 = yellow_points[-1]
                
                # 计算角度（弧度）
                dx = p2[0] - p1[0]
                dy = p2[1] - p1[1]
                
                if dx != 0:
                    # 计算角度（度数）
                    angle_rad = np.arctan2(dy, dx)
                    angle_deg = np.degrees(angle_rad)
                    
                    self.logger.info(f"最后两个数据点: {p1} -> {p2}")
                    self.logger.info(f"黄线角度: {angle_deg:.2f}度 (dx={dx}, dy={dy})")
                    
                    # 检查是否有历史数据点进行比较
                    if not hasattr(self, 'last_yellow_points'):
                        self.last_yellow_points = yellow_points
                        self.logger.info("初始化黄线数据点")
                        return {'signal': 'none', 'direction': 'stable', 'area': area, 'angle': angle_deg, 'position': (x, y)}
                    
                    # 计算与上一个数据点的角度变化
                    if len(self.last_yellow_points) >= 2:
                        last_p1 = self.last_yellow_points[-2]
                        last_p2 = self.last_yellow_points[-1]
                        
                        last_dx = last_p2[0] - last_p1[0]
                        last_dy = last_p2[1] - last_p1[1]
                        
                        if last_dx != 0:
                            last_angle_rad = np.arctan2(last_dy, last_dx)
                            last_angle_deg = np.degrees(last_angle_rad)
                            
                            # 计算角度变化
                            angle_change = abs(angle_deg - last_angle_deg)
                            self.logger.info(f"角度变化: {angle_change:.2f}度 (当前: {angle_deg:.2f}度, 上次: {last_angle_deg:.2f}度)")
                            
                            # 判断交易信号（角度变化>20度，符合图片需求）
                            if angle_change > 20:  # 调整为20度，符合图片需求
                                if angle_deg > last_angle_deg:
                                    self.logger.info(f"✅ 检测到黄线上涨信号 (角度变化: {angle_change:.2f}度)")
                                    return {'signal': 'up', 'direction': 'significant', 'area': area, 'angle': angle_deg, 'angle_change': angle_change, 'position': (x, y)}
                                else:
                                    self.logger.info(f"✅ 检测到黄线下跌信号 (角度变化: {angle_change:.2f}度)")
                                    return {'signal': 'down', 'direction': 'significant', 'area': area, 'angle': angle_deg, 'angle_change': angle_change, 'position': (x, y)}
                            else:
                                self.logger.info(f"角度变化不足: {angle_change:.2f}度 < 20度")
                                return {'signal': 'none', 'direction': 'stable', 'area': area, 'angle': angle_deg, 'angle_change': angle_change, 'position': (x, y)}
                        else:
                            self.logger.info("上次数据点X坐标相同，无法计算角度")
                            return {'signal': 'none', 'direction': 'stable', 'area': area, 'angle': angle_deg, 'position': (x, y)}
                    else:
                        self.logger.info("历史数据点不足")
                        return {'signal': 'none', 'direction': 'stable', 'area': area, 'angle': angle_deg, 'position': (x, y)}
                else:
                    self.logger.info("当前数据点X坐标相同，无法计算角度")
                    return {'signal': 'none', 'direction': 'stable', 'area': area, 'angle': 0.0, 'position': (x, y)}
            
            self.logger.info("无法计算黄线角度")
            return {'signal': 'none', 'direction': 'stable', 'area': area, 'angle': 0.0, 'position': (x, y)}
            
        except Exception as e:
            self.logger.error(f"黄线检测错误: {e}")
            return {'signal': 'none', 'direction': 'error', 'area': 0, 'angle': 0.0, 'position': (0, 0)}
        finally:
            # 修复：每次检测后都更新last_yellow_points，确保下次能正确计算角度变化
            try:
                if 'yellow_points' in locals() and len(yellow_points) >= 5:
                    self.last_yellow_points = yellow_points
                    self.logger.info("已更新黄线历史数据点")
            except Exception as e:
                self.logger.error(f"更新黄线历史数据点失败: {e}")
    
    def detect_price_change(self, screen: np.ndarray) -> Dict:
        """检测价格变化"""
        try:
            self.logger.info("开始价格检测...")

            if not TESSERACT_AVAILABLE:
                self.logger.warning("Tesseract不可用，价格检测功能将不可用")
                return {'detected': False, 'direction': 'none', 'price': 0.0}

            height, width = screen.shape[:2]
            self.logger.info(f"屏幕尺寸: {width} x {height}")

            # 扩大价格检测区域：右侧面板的更大区域
            price_region = screen[int(height*0.2):int(height*0.6), int(width*0.4):int(width*0.9)]
            self.logger.info(f"价格检测区域尺寸: {price_region.shape[1]} x {price_region.shape[0]}")

            # 保存调试图像
            debug_price_filename = f"debug_price_region_{int(time.time())}.png"
            success = cv2.imwrite(debug_price_filename, price_region)
            if success:
                self.logger.info(f"✅ 保存价格检测区域截图: {debug_price_filename}")
            else:
                self.logger.error(f"❌ 保存价格检测区域截图失败: {debug_price_filename}")

            # 图像预处理
            gray = cv2.cvtColor(price_region, cv2.COLOR_BGR2GRAY)

            # 增强对比度
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)

            # 降噪
            denoised = cv2.medianBlur(enhanced, 3)

            # 二值化
            _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 尝试多种OCR配置
            ocr_configs = [
                '--psm 8 --oem 3 -c tessedit_char_whitelist=0123456789.',
                '--psm 7 --oem 3 -c tessedit_char_whitelist=0123456789.',
                '--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789.',
                '--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789.'
            ]

            best_text = ""
            for i, config in enumerate(ocr_configs):
                try:
                    text = pytesseract.image_to_string(binary, config=config)
                    text = text.strip()
                    self.logger.info(f"OCR配置{i+1}识别文本: '{text}'")

                    if text and len(text) > 0:
                        best_text = text
                        self.logger.info(f"使用OCR配置{i+1}")
                        break
                except Exception as e:
                    self.logger.error(f"OCR配置{i+1}失败: {e}")

            self.logger.info(f"最终OCR识别文本 (价格): '{best_text}'")

            # 提取数字
            import re
            numbers = re.findall(r'\d+\.?\d*', best_text)
            self.logger.info(f"提取的数字: {numbers}")

            if not numbers:
                self.logger.info("价格监测: 未检测到价格 (OCR无文本)")
                return {'detected': False, 'direction': 'none', 'price': 0.0}

            # 尝试解析价格
            valid_prices = []
            for num_str in numbers:
                try:
                    price = float(num_str)
                    # 扩大价格范围
                    if 100 <= price <= 2000:  # 从1300-1500改为100-2000
                        valid_prices.append(price)
                except ValueError:
                    continue

            if not valid_prices:
                self.logger.info("价格监测: 未检测到有效价格")
                return {'detected': False, 'direction': 'none', 'price': 0.0}

            # 选择最合理的价格（通常是最大的数字）
            current_price = max(valid_prices)
            self.logger.info(f"检测到价格: {current_price}")

            # 检查价格变化
            if not hasattr(self, 'last_price'):
                self.last_price = current_price
                self.logger.info(f"初始化价格: {current_price}")
                return {'detected': True, 'direction': 'none', 'price': current_price}

            price_change = current_price - self.last_price
            self.logger.info(f"价格变化: {self.last_price} -> {current_price} (变化: {price_change:.2f})")

            # 降低价格变化阈值
            min_price_change = 0.01  # 从0.05改为0.01

            if abs(price_change) < min_price_change:
                self.logger.info(f"价格变化太小: {price_change:.2f}")
                return {'detected': True, 'direction': 'none', 'price': current_price}

            # 判断方向
            if price_change > 0:
                direction = 'up'
                self.logger.info(f"✅ 检测到价格上涨: {price_change:.2f}")
            else:
                direction = 'down'
                self.logger.info(f"✅ 检测到价格下跌: {price_change:.2f}")

            self.last_price = current_price
            self.logger.info(f"价格监测: {direction} - {current_price}")
            return {'detected': True, 'direction': direction, 'price': current_price}

        except Exception as e:
            self.logger.error(f"价格检测错误: {e}")
            return {'detected': False, 'direction': 'error', 'price': 0.0}

    def detect_profit_change(self, screen: np.ndarray) -> Dict:
        """检测货值变化"""
        try:
            # 检查转让冷却期
            current_time = time.time()
            if current_time - self.last_transfer_time < self.transfer_cooldown:
                remaining_cooldown = self.transfer_cooldown - (current_time - self.last_transfer_time)
                self.logger.info(f"转让冷却中，剩余 {remaining_cooldown:.1f} 秒")
                return {'detected': False, 'direction': 'cooldown', 'value': 0.0}
            
            self.logger.info("开始实时货值变化检测...")
            import pytesseract
            
            # 设置Tesseract路径
            pytesseract.pytesseract.tesseract_cmd = r"D:\Program Files\tesseract.exe"
            
            height, width = screen.shape[:2]
            self.logger.info(f"屏幕尺寸: {width} x {height}")
            
            # 货值变化检测区域：右侧面板的更大区域
            profit_region = screen[int(height*0.3):int(height*0.8), int(width*0.6):int(width*0.95)]
            self.logger.info(f"货值变化检测区域尺寸: {profit_region.shape[1]} x {profit_region.shape[0]}")
        
            # 保存调试图像
            debug_profit_filename = f"debug_profit_region_{int(time.time())}.png"
            success = cv2.imwrite(debug_profit_filename, profit_region)
            if success:
                self.logger.info(f"✅ 保存货值变化检测区域截图: {debug_profit_filename}")
            else:
                self.logger.error(f"❌ 保存货值变化检测区域截图失败: {debug_profit_filename}")
            
            # 图像预处理
            gray = cv2.cvtColor(profit_region, cv2.COLOR_BGR2GRAY)
            
            # 增强对比度
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            
            # 降噪
            denoised = cv2.medianBlur(enhanced, 3)
            
            # 二值化
            _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 尝试多种OCR配置
            ocr_configs = [
                '--psm 8 --oem 3 -c tessedit_char_whitelist=0123456789.-',
                '--psm 7 --oem 3 -c tessedit_char_whitelist=0123456789.-',
                '--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789.-',
                '--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789.-'
            ]
            
            best_text = ""
            for i, config in enumerate(ocr_configs):
                try:
                    text = pytesseract.image_to_string(binary, config=config)
                    text = text.strip()
                    self.logger.info(f"OCR配置{i+1}识别文本 (货值): '{text}'")
                    
                    if text and len(text) > 0:
                        best_text = text
                        self.logger.info(f"使用OCR配置{i+1}")
                        break
                except Exception as e:
                    self.logger.error(f"OCR配置{i+1}失败: {e}")
            
            self.logger.info(f"最终OCR识别文本 (货值变化): '{best_text}'")
            
            # 提取数字
            import re
            numbers = re.findall(r'-?\d+\.?\d*', best_text)
            self.logger.info(f"提取的数字 (货值): {numbers}")
            
            if not numbers:
                self.logger.info("货值监测: 未检测到货值变化 (OCR无文本)")
                return {'detected': False, 'direction': 'none', 'value': 0.0}
            
            # 尝试解析货值变化
            valid_values = []
            for num_str in numbers:
                try:
                    value = float(num_str)
                    # 扩大货值范围
                    if -1000 <= value <= 1000:  # 从-100到100改为-1000到1000
                        valid_values.append(value)
                except ValueError:
                    continue
            
            if not valid_values:
                self.logger.info("货值监测: 未检测到有效货值变化")
                return {'detected': False, 'direction': 'none', 'value': 0.0}
            
            # 选择最合理的货值变化（通常是绝对值最大的数字）
            current_profit = max(valid_values, key=abs)
            self.logger.info(f"检测到货值变化: {current_profit}")
            
            # 检查货值变化
            if self.last_profit_value is None:
                self.last_profit_value = current_profit
                self.logger.info(f"初始化货值: {current_profit}")
                return {'detected': True, 'direction': 'none', 'value': current_profit}
            
            profit_change = current_profit - self.last_profit_value
            self.logger.info(f"货值变化: {self.last_profit_value} -> {current_profit} (变化: {profit_change:.2f})")
            
            # 提高货值变化阈值，减少误触发
            if abs(profit_change) < self.profit_change_threshold:
                self.logger.info(f"货值变化太小: {profit_change:.2f}")
                return {'detected': True, 'direction': 'none', 'value': current_profit}
            
            # 检查止盈止损条件（符合图片需求：≥1或≤-3）
            if current_profit >= 1.0:
                self.logger.info(f"✅ 触发止盈条件: 货值变化 {current_profit} >= 1.0")
                self.last_profit_value = current_profit
                return {'detected': True, 'direction': 'profit_take', 'value': current_profit, 'should_transfer': True}
            elif current_profit <= -3.0:
                self.logger.info(f"✅ 触发止损条件: 货值变化 {current_profit} <= -3.0")
                self.last_profit_value = current_profit
                return {'detected': True, 'direction': 'loss_stop', 'value': current_profit, 'should_transfer': True}
            
            # 判断方向
            if profit_change > 0:
                direction = 'up'
                self.logger.info(f"✅ 检测到货值上涨: {profit_change:.2f}")
            else:
                direction = 'down'
                self.logger.info(f"✅ 检测到货值下跌: {profit_change:.2f}")
            
            self.last_profit_value = current_profit
            self.logger.info(f"货值监测: {direction} - {current_profit}")
            return {'detected': True, 'direction': direction, 'value': current_profit}
            
        except Exception as e:
            self.logger.error(f"货值检测错误: {e}")
            return {'detected': False, 'direction': 'error', 'value': 0.0}

    def detect_enhanced_signals(self, screenshot: np.ndarray) -> Dict:
        """检测增强信号 - K线+MACD+均线组合策略"""
        try:
            if not ENHANCED_DETECTION_AVAILABLE:
                self.logger.warning("增强检测模块不可用，使用原有检测方法")
                return {'signal': 'hold', 'confidence': 0.0, 'method': 'fallback'}

            # 获取综合信号
            result = enhanced_detector.get_comprehensive_signal(screenshot)

            if result['final_signal'] == SignalType.BULLISH:
                signal = 'buy'
                self.logger.info(f"🟢 增强检测: 看涨信号 (置信度: {result['final_confidence']:.2f})")
            elif result['final_signal'] == SignalType.BEARISH:
                signal = 'sell'
                self.logger.info(f"🔴 增强检测: 看跌信号 (置信度: {result['final_confidence']:.2f})")
            else:
                signal = 'hold'
                self.logger.info(f"⚪ 增强检测: 中性信号 (置信度: {result['final_confidence']:.2f})")

            # 记录详细信息
            if 'kline' in result:
                kline_info = result['kline']
                self.logger.info(f"K线信号: {kline_info.get('signal', 'unknown')} "
                               f"(红色比例: {kline_info.get('red_ratio', 0):.3f}, "
                               f"绿色比例: {kline_info.get('green_ratio', 0):.3f})")

            if 'macd' in result:
                macd_info = result['macd']
                self.logger.info(f"MACD信号: {macd_info.get('signal', 'unknown')} "
                               f"(最新红柱: {macd_info.get('latest_red', 0)}, "
                               f"最新绿柱: {macd_info.get('latest_green', 0)})")

            return {
                'signal': signal,
                'confidence': result['final_confidence'],
                'method': 'enhanced',
                'details': result
            }

        except Exception as e:
            self.logger.error(f"增强信号检测失败: {e}")
            return {'signal': 'hold', 'confidence': 0.0, 'method': 'error', 'error': str(e)}

    def detect_jingtao_signals(self, screenshot: np.ndarray) -> Dict:
        """景陶易购专用信号检测 - 使用修复的颜色检测"""
        try:
            if not self.jingtao_detector:
                self.logger.warning("景陶易购专用检测器不可用")
                return {'signal': 'none', 'confidence': 0.0, 'method': 'unavailable'}
            
            height, width = screenshot.shape[:2]
            self.logger.info(f"景陶易购信号检测 - 屏幕尺寸: {width} x {height}")
            
            # 定义检测区域（根据您的界面调整）
            kline_region = screenshot[
                int(height*0.12):int(height*0.67),
                int(width*0.05):int(width*0.70)
            ]
            
            macd_region = screenshot[
                int(height*0.72):int(height*0.87),
                int(width*0.05):int(width*0.70)
            ]
            
            # 保存调试图像
            debug_timestamp = int(time.time())
            cv2.imwrite(f"debug_jingtao_kline_{debug_timestamp}.png", kline_region)
            cv2.imwrite(f"debug_jingtao_macd_{debug_timestamp}.png", macd_region)
            
            # K线检测
            kline_result = self.jingtao_detector.detect_kline_trend(kline_region)
            self.logger.info(f"📊 K线检测结果: {kline_result}")
            
            # MACD检测
            macd_result = self.jingtao_detector.detect_macd_signal(macd_region)
            self.logger.info(f"📈 MACD检测结果: {macd_result}")
            
            # 信号融合
            final_signal, final_confidence = self._fuse_jingtao_signals(kline_result, macd_result)
            
            # 记录详细信息
            self.logger.info(f"🎯 景陶易购最终信号: {final_signal} (置信度: {final_confidence:.3f})")
            
            return {
                'signal': final_signal,
                'confidence': final_confidence,
                'method': 'jingtao_enhanced',
                'kline_details': kline_result,
                'macd_details': macd_result,
                'timestamp': debug_timestamp
            }
            
        except Exception as e:
            self.logger.error(f"景陶易购信号检测失败: {e}")
            return {'signal': 'none', 'confidence': 0.0, 'method': 'error'}
    
    def _fuse_jingtao_signals(self, kline_result: Dict, macd_result: Dict) -> tuple:
        """融合景陶易购K线和MACD信号"""
        try:
            # 权重配置
            kline_weight = 0.6
            macd_weight = 0.4
            
            # 信号值转换
            def signal_to_value(signal):
                if signal == 'buy':
                    return 1.0
                elif signal == 'sell':
                    return -1.0
                else:
                    return 0.0
            
            kline_value = signal_to_value(kline_result.get('signal', 'none'))
            macd_value = signal_to_value(macd_result.get('signal', 'none'))
            
            kline_confidence = kline_result.get('confidence', 0.0)
            macd_confidence = macd_result.get('confidence', 0.0)
            
            # 加权融合
            kline_score = kline_value * kline_confidence * kline_weight
            macd_score = macd_value * macd_confidence * macd_weight
            
            total_score = kline_score + macd_score
            total_weight = kline_weight + macd_weight
            
            # 计算最终置信度
            final_confidence = abs(total_score) / total_weight if total_weight > 0 else 0.0
            
            # 判断最终信号 (降低阈值，提高灵敏度)
            if total_score > 0.3:  # 降低买入阈值
                final_signal = 'buy'
            elif total_score < -0.3:  # 降低卖出阈值
                final_signal = 'sell'
            else:
                final_signal = 'none'
            
            self.logger.info(f"信号融合: K线({kline_result['signal']}, {kline_confidence:.3f}) + "
                           f"MACD({macd_result['signal']}, {macd_confidence:.3f}) = "
                           f"{final_signal}({final_confidence:.3f})")
            
            return final_signal, final_confidence
            
        except Exception as e:
            self.logger.error(f"信号融合失败: {e}")
            return 'none', 0.0

    def analyze_trade_signal(self, yellow_signal: Dict, enhanced_signal: Dict = None) -> Dict:
        """分析交易信号 - 基于黄线角度变化和增强检测信号"""
        try:
            # 检查转让冷却期
            current_time = time.time()
            if current_time - self.last_transfer_time < self.transfer_cooldown:
                remaining_cooldown = self.transfer_cooldown - (current_time - self.last_transfer_time)
                self.logger.info(f"转让冷却中，剩余 {remaining_cooldown:.1f} 秒")
                return {
                    'should_trade': False,
                    'action': 'none',
                    'reason': f'转让冷却中 ({remaining_cooldown:.1f}秒)'
                }
            
            # 优先级1：检查增强信号（如果可用且置信度足够）
            if enhanced_signal and enhanced_signal.get('confidence', 0) > 0.6:
                signal_type = enhanced_signal.get('signal', 'hold')
                confidence = enhanced_signal.get('confidence', 0)

                if signal_type == 'buy':
                    return {
                        'should_trade': True,
                        'action': 'buy',
                        'reason': f'增强检测买入信号 (置信度: {confidence:.2f})'
                    }
                elif signal_type == 'sell':
                    return {
                        'should_trade': True,
                        'action': 'sell',
                        'reason': f'增强检测卖出信号 (置信度: {confidence:.2f})'
                    }

            # 优先级2：检查黄线信号（作为备用策略）
            if yellow_signal['signal'] == 'none':
                return {
                    'should_trade': False,
                    'action': 'none',
                    'reason': '黄线无变化'
                }
            
            # 直接基于黄线角度变化判断交易
            yellow_direction = yellow_signal['signal']  # 'up' 或 'down'
            angle_change = yellow_signal.get('angle_change', 0)
            
            # 交易逻辑：
            # 1. 黄线上涨（角度变化>20度）= 买入
            # 2. 黄线下跌（角度变化>20度）= 卖出
            
            if yellow_direction == 'up' and angle_change > 20:  # 调整为20度，符合图片需求
                return {
                    'should_trade': True,
                    'action': 'buy',
                    'reason': f'黄线上涨信号 (角度变化: {angle_change:.2f}度)'
                }
            elif yellow_direction == 'down' and angle_change > 20:  # 调整为20度，符合图片需求
                return {
                    'should_trade': True,
                    'action': 'sell',
                    'reason': f'黄线下跌信号 (角度变化: {angle_change:.2f}度)'
                }
            else:
                return {
                    'should_trade': False,
                    'action': 'none',
                    'reason': f'角度变化不足: {angle_change:.2f}度 < 20度'
                }
                
        except Exception as e:
            self.logger.error(f"交易信号分析错误: {e}")
            return {
                'should_trade': False,
                'action': 'none',
                'reason': f'分析错误: {e}'
            }
    
    def execute_trade(self, signal: Dict):
        """执行交易"""
        try:
            self.logger.info(f"🚀 准备执行交易: {signal}")
            
            # 执行前坐标验证
            required_buttons = []
            if signal['action'] == 'buy':
                required_buttons = ['buy_mode_button', 'price_input', 'quantity_input', 'buy_order_button']
            elif signal['action'] == 'sell':
                required_buttons = ['sell_mode_button', 'price_input', 'quantity_input', 'sell_order_button']
            elif signal['action'] == 'transfer':
                required_buttons = ['transfer_out_button']
            
            # 快速验证关键按钮坐标
            if required_buttons:
                self.logger.info("🎯 验证交易相关按钮坐标...")
                coord_test_passed = self.test_coordinates(required_buttons)
                if not coord_test_passed:
                    self.logger.warning("⚠️ 坐标验证未完全通过，但继续执行交易")
            
            # 激活客户端窗口
            windows = pyautogui.getWindowsWithTitle("景陶易购")
            if windows:
                windows[0].activate()
                time.sleep(1)
                self.logger.info("✅ 客户端窗口已激活")
            else:
                self.logger.error("❌ 无法找到客户端窗口")
                return
            
            # 执行交易操作
            if signal['action'] == 'buy':
                self.logger.info("开始执行买入订单...")
                success = self._execute_buy_order(signal.get('price', 1383), 1)
                if success:
                    self.logger.info("买入订单执行成功")
                else:
                    self.logger.error("买入订单执行失败")
            elif signal['action'] == 'sell':
                self.logger.info("开始执行卖出订单...")
                success = self._execute_sell_order(signal.get('price', 1383), 1)
                if success:
                    self.logger.info("卖出订单执行成功")
                else:
                    self.logger.error("卖出订单执行失败")
            elif signal['action'] == 'transfer':
                self.logger.info(f"开始执行转让操作: {signal.get('transfer_type', 'unknown')}")
                success = self._execute_transfer_order(signal.get('transfer_type', 'transfer_loss'))
                if success:
                    self.logger.info("转让操作执行成功")
                    # 更新转让冷却期
                    self.last_transfer_time = time.time()
                    self.logger.info(f"转让冷却期已重置，下次转让需等待 {self.transfer_cooldown} 秒")
                else:
                    self.logger.error("转让操作执行失败")
            
            self.last_trade_time = time.time()
            self.logger.info("交易执行完成")
            
        except Exception as e:
            self.logger.error(f"执行交易失败: {e}")
    
    def _execute_buy_order(self, price: float, quantity: int = 1):
        """执行买入订单"""
        try:
            self.logger.info(f"执行买入订单: 价格={price}, 数量={quantity}")
            
            # 1. 点击买入模式按钮
            x, y = self.calculate_button_position('buy_mode_button')
            if x is not None and y is not None:
                pyautogui.click(x, y)
                self.logger.info("点击买入模式按钮")
                time.sleep(0.5)
            
            # 2. 输入价格
            x, y = self.calculate_button_position('price_input')
            if x is not None and y is not None:
                pyautogui.click(x, y)
                time.sleep(0.2)
                pyautogui.hotkey('ctrl', 'a')  # 全选
                time.sleep(0.1)
                pyautogui.write(str(price))
                self.logger.info(f"输入价格: {price}")
                time.sleep(0.2)
            
            # 3. 输入数量
            x, y = self.calculate_button_position('quantity_input')
            if x is not None and y is not None:
                pyautogui.click(x, y)
                time.sleep(0.2)
                pyautogui.hotkey('ctrl', 'a')  # 全选
                time.sleep(0.1)
                pyautogui.write(str(quantity))
                self.logger.info(f"输入数量: {quantity}")
                time.sleep(0.2)
            
            # 4. 点击确认按钮
            x, y = self.calculate_button_position('confirm_button')
            if x is not None and y is not None:
                pyautogui.click(x, y)
                self.logger.info("点击确认按钮")
                time.sleep(0.5)
            
            # 5. 点击买入订立按钮
            x, y = self.calculate_button_position('buy_order_button')
            if x is not None and y is not None:
                pyautogui.click(x, y)
                self.logger.info("点击买入订立按钮")
                
                # 6. 等待确认对话框出现（1-2秒延迟）然后自动确认
                if self.auto_confirm_handler:
                    self.logger.info("等待1-2秒让确认对话框出现...")
                    time.sleep(1.5)  # 先等待1.5秒让对话框出现
                    
                    self.logger.info("开始检测并确认对话框...")
                    if self.auto_confirm_handler.wait_and_confirm(max_wait_time=2.0):
                        self.logger.info("✅ 自动确认对话框成功")
                    else:
                        self.logger.warning("⚠️ 未检测到确认对话框，可能需要手动确认")
                else:
                    self.logger.warning("⚠️ 自动确认功能不可用，请手动确认对话框")
                    time.sleep(3)  # 给用户手动确认的时间
            
            self.logger.info("买入订单执行完成")
            
        except Exception as e:
            self.logger.error(f"买入订单执行失败: {e}")
    
    def _execute_sell_order(self, price: float, quantity: int = 1):
        """执行卖出订单"""
        try:
            self.logger.info(f"执行卖出订单: 价格={price}, 数量={quantity}")

            # 1. 点击卖出模式按钮
            x, y = self.calculate_button_position('sell_mode_button')
            if x is not None and y is not None:
                pyautogui.click(x, y)
                self.logger.info("点击卖出模式按钮")
                time.sleep(0.5)

            # 2. 输入价格
            x, y = self.calculate_button_position('price_input')
            if x is not None and y is not None:
                pyautogui.click(x, y)
                time.sleep(0.2)
                pyautogui.hotkey('ctrl', 'a')  # 全选
                time.sleep(0.1)
                pyautogui.write(str(price))
                self.logger.info(f"输入价格: {price}")
                time.sleep(0.2)

            # 3. 输入数量
            x, y = self.calculate_button_position('quantity_input')
            if x is not None and y is not None:
                pyautogui.click(x, y)
                time.sleep(0.2)
                pyautogui.hotkey('ctrl', 'a')  # 全选
                time.sleep(0.1)
                pyautogui.write(str(quantity))
                self.logger.info(f"输入数量: {quantity}")
                time.sleep(0.2)

            # 4. 点击确认按钮
            x, y = self.calculate_button_position('confirm_button')
            if x is not None and y is not None:
                pyautogui.click(x, y)
                self.logger.info("点击确认按钮")
                time.sleep(0.5)

            # 5. 点击卖出订立按钮
            x, y = self.calculate_button_position('sell_order_button')
            if x is not None and y is not None:
                pyautogui.click(x, y)
                self.logger.info("点击卖出订立按钮")
                
                # 6. 等待确认对话框出现（1-2秒延迟）然后自动确认
                if self.auto_confirm_handler:
                    self.logger.info("等待1-2秒让确认对话框出现...")
                    time.sleep(1.5)  # 先等待1.5秒让对话框出现
                    
                    self.logger.info("开始检测并确认对话框...")
                    if self.auto_confirm_handler.wait_and_confirm(max_wait_time=2.0):
                        self.logger.info("✅ 自动确认对话框成功")
                    else:
                        self.logger.warning("⚠️ 未检测到确认对话框，可能需要手动确认")
                else:
                    self.logger.warning("⚠️ 自动确认功能不可用，请手动确认对话框")
                    time.sleep(3)  # 给用户手动确认的时间

            self.logger.info("卖出订单执行完成")

        except Exception as e:
            self.logger.error(f"卖出订单执行失败: {e}")

    def _execute_transfer_order(self, action: str):
        """执行转让订单（止盈/止损）"""
        try:
            if action == 'transfer_profit':
                self.logger.info("执行止盈转让...")
            elif action == 'transfer_loss':
                self.logger.info("执行止损转让...")
            else:
                self.logger.warning(f"未知的转让动作: {action}")
                return False

            # 激活客户端窗口
            if self.client_window:
                self.client_window.activate()
                time.sleep(1)
                self.logger.info("客户端窗口已激活")

            # 点击转让按钮
            x, y = self.calculate_button_position('transfer_out_button')
            if x is not None and y is not None:
                pyautogui.click(x, y)
                self.logger.info("点击转让按钮")
                time.sleep(1)

                # 可能需要确认转让操作
                # 这里可以添加额外的确认步骤，如果界面需要的话

                self.logger.info("转让订单执行完成")
                return True
            else:
                self.logger.error("无法找到转让按钮位置")
                return False

        except Exception as e:
            self.logger.error(f"转让订单执行失败: {e}")
            return False

    def stop(self):
        """停止交易"""
        self.is_running = False
        # 不再终止客户端进程，只停止监测
        self.logger.info("智能交易引擎已停止")

class TradingThread(QThread):
    """交易线程"""
    log_signal = pyqtSignal(str)
    status_signal = pyqtSignal(str)
    
    def __init__(self, trading_engine):
        super().__init__()
        self.trading_engine = trading_engine
        self.is_running = False
    
    def run(self):
        """运行交易监测"""
        try:
            self.is_running = True
            self.log_signal.emit("开始智能监测和交易...")
            
            # 检测客户端并进行校准
            if not self.trading_engine.start_client():
                self.log_signal.emit("❌ 客户端检测或校准失败")
                self.log_signal.emit("请确保：")
                self.log_signal.emit("1. 景陶易购客户端已打开")
                self.log_signal.emit("2. 客户端窗口可见且未被最小化")
                self.log_signal.emit("3. 重新点击'启动智能交易'")
                return
            
            self.log_signal.emit("✅ 客户端检测和按钮校准成功")
            self.log_signal.emit("开始监测黄线变化和价格变化...")
            
            # 开始监测
            while self.is_running:
                try:
                    # 确保客户端窗口存在
                    if not self.trading_engine.client_window:
                        self.log_signal.emit("客户端窗口丢失，重新查找...")
                        if not self.trading_engine.find_client_window():
                            self.log_signal.emit("无法找到客户端窗口")
                            time.sleep(10)
                            continue
                    
                    # 截取客户端窗口区域
                    if self.trading_engine.window_rect:
                        screen = pyautogui.screenshot(region=(
                            self.trading_engine.window_rect.left,
                            self.trading_engine.window_rect.top,
                            self.trading_engine.window_rect.width,
                            self.trading_engine.window_rect.height
                        ))
                        screen_array = np.array(screen)
                        
                        # 保存调试截图（每30秒保存一次）
                        current_time = time.time()
                        if not hasattr(self, 'last_debug_save') or current_time - self.last_debug_save > 30:
                            debug_filename = f"debug_screenshot_{int(current_time)}.png"
                            screen.save(debug_filename)
                            self.log_signal.emit(f"保存调试截图: {debug_filename}")
                            self.last_debug_save = current_time
                        
                        # 监测黄线
                        yellow_signal = self.trading_engine.detect_yellow_line_change(screen_array)

                        # 记录监测结果
                        if yellow_signal['signal'] != 'none':
                            angle_change = yellow_signal.get('angle_change', 0)
                            self.log_signal.emit(f"黄线监测: {yellow_signal['signal']} - 角度变化: {angle_change:.2f}度")
                        else:
                            self.log_signal.emit(f"黄线监测: none - stable")

                        # 景陶易购专用检测（优先使用）
                        enhanced_signal = None
                        if hasattr(self.trading_engine, 'detect_jingtao_signals'):
                            enhanced_signal = self.trading_engine.detect_jingtao_signals(screen_array)
                            if enhanced_signal['signal'] != 'none':
                                self.log_signal.emit(f"🎯 景陶易购检测: {enhanced_signal['signal']} "
                                                   f"(置信度: {enhanced_signal['confidence']:.2f})")
                                
                        # 备用增强检测（如果景陶易购检测不可用）
                        if enhanced_signal is None or enhanced_signal['signal'] == 'none':
                            if ENHANCED_DETECTION_AVAILABLE:
                                fallback_signal = self.trading_engine.detect_enhanced_signals(screen_array)
                                if fallback_signal['signal'] != 'hold':
                                    enhanced_signal = fallback_signal
                                    self.log_signal.emit(f"🔍 备用检测: {enhanced_signal['signal']} "
                                                       f"(置信度: {enhanced_signal['confidence']:.2f})")

                        # 分析交易信号（结合黄线和增强检测）
                        trade_signal = self.trading_engine.analyze_trade_signal(yellow_signal, enhanced_signal)

                        if trade_signal['should_trade']:
                            self.log_signal.emit(f"🎯 触发交易信号: {trade_signal['reason']}")

                            # 执行交易
                            self.trading_engine.execute_trade(trade_signal)
                            self.log_signal.emit("✅ 交易执行完成")
                        else:
                            self.log_signal.emit(f"📊 无交易信号: {trade_signal['reason']}")
                    else:
                        self.log_signal.emit("无法获取客户端窗口位置")
                    
                    time.sleep(5)  # 每5秒检查一次
                    
                except Exception as e:
                    self.log_signal.emit(f"监测异常: {e}")
                    time.sleep(10)
            
        except Exception as e:
            self.log_signal.emit(f"交易线程异常: {e}")
    
    def stop(self):
        """停止线程"""
        self.is_running = False

class SmartTradingWindow(QMainWindow):
    """智能交易窗口"""
    
    def __init__(self):
        super().__init__()
        self.trading_engine = SmartTradingEngine()
        self.trading_thread = None
        
        self.setup_ui()
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("智能交易系统 - 景陶易购")
        self.setGeometry(100, 100, 800, 600)
        
        # 主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("智能交易系统")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("启动智能交易")
        self.start_button.clicked.connect(self.start_trading)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止交易")
        self.stop_button.clicked.connect(self.stop_trading)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        self.test_coords_button = QPushButton("测试坐标")
        self.test_coords_button.clicked.connect(self.test_coordinates_ui)
        button_layout.addWidget(self.test_coords_button)
        
        layout.addLayout(button_layout)
        
        # 状态显示
        status_group = QGroupBox("系统状态")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("状态: 已停止")
        self.status_label.setFont(QFont("Microsoft YaHei", 12))
        status_layout.addWidget(self.status_label)
        
        layout.addWidget(status_group)
        
        # 日志显示
        log_group = QGroupBox("运行日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 10))
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QTextEdit {
                background-color: white;
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 8px;
            }
        """)
    
    def start_trading(self):
        """启动交易"""
        try:
            self.log_text.append("正在启动智能交易系统...")
            
            # 创建交易线程
            self.trading_thread = TradingThread(self.trading_engine)
            self.trading_thread.log_signal.connect(self.update_log)
            self.trading_thread.status_signal.connect(self.update_status)
            
            # 启动线程
            self.trading_thread.start()
            
            # 更新界面状态
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("状态: 运行中")
            
            self.log_text.append("智能交易系统已启动")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动失败: {e}")
            self.log_text.append(f"启动失败: {e}")
    
    def stop_trading(self):
        """停止交易"""
        try:
            if self.trading_thread:
                self.trading_thread.stop()
                self.trading_thread.wait()
            
            self.trading_engine.stop()
            
            # 更新界面状态
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText("状态: 已停止")
            
            self.log_text.append("智能交易系统已停止")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止失败: {e}")
            self.log_text.append(f"停止失败: {e}")
    
    def update_log(self, message: str):
        """更新日志"""
        self.log_text.append(f"{time.strftime('%H:%M:%S')} - {message}")
        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )
    
    def update_status(self, status: str):
        """更新状态"""
        self.status_label.setText(f"状态: {status}")
    
    def test_coordinates_ui(self):
        """UI界面的坐标测试功能"""
        try:
            self.log_text.append("🎯 开始坐标测试...")
            
            # 检查客户端是否连接
            if not self.trading_engine.find_client_window():
                self.log_text.append("❌ 未找到客户端窗口，请先启动景陶易购客户端")
                QMessageBox.warning(self, "警告", "未找到客户端窗口，请先启动景陶易购客户端")
                return
            
            # 比较窗口尺寸
            self.trading_engine.compare_window_dimensions()
            
            # 测试所有关键按钮坐标
            all_buttons = [
                'buy_mode_button', 'sell_mode_button', 'buy_order_button', 
                'sell_order_button', 'price_input', 'quantity_input', 
                'confirm_button', 'transfer_out_button'
            ]
            
            test_result = self.trading_engine.test_coordinates(all_buttons)
            
            if test_result:
                self.log_text.append("✅ 坐标测试通过")
                QMessageBox.information(self, "测试结果", "✅ 坐标测试通过，所有按钮位置正确")
            else:
                self.log_text.append("⚠️ 坐标测试发现问题，请查看详细日志")
                QMessageBox.warning(self, "测试结果", "⚠️ 坐标测试发现问题，部分按钮位置可能不准确\n\n请查看详细日志或考虑重新校准")
            
        except Exception as e:
            error_msg = f"坐标测试失败: {e}"
            self.log_text.append(f"❌ {error_msg}")
            QMessageBox.critical(self, "错误", error_msg)
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.trading_thread and hasattr(self.trading_thread, 'is_running') and self.trading_thread.is_running:
            self.stop_trading()
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("智能交易系统")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = SmartTradingWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 
